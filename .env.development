# 页面标题
VITE_APP_TITLE = 生物入侵跨境传入与发生扩散风险分析系统 
VITE_APP_ENG_TITLE = Cross-border Introduction and Spread Risk Analysis System for Biological Invasions
# 开发环境配置
VITE_APP_ENV = 'development'

# 若依管理系统/开发环境
VITE_APP_BASE_API = '/dev-api'

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'

# 图片预览地址
VITE_APP_PREVIEW_URL = http://jcy.nat300.top/file

# 监控地址
VITE_APP_MONITRO_ADMIN = 'http://localhost:9090/admin/applications'

# xxl-job 控制台地址
VITE_APP_XXL_JOB_ADMIN = 'http://localhost:9100/xxl-job-admin'

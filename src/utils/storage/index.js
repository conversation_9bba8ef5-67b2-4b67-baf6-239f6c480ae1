export function createLocalStorage(options) {
  const DEFAULT_CACHE_TIME = 60 * 60 * 24 * 7;

  const { expire } = Object.assign({ expire: DEFAULT_CACHE_TIME }, options);

  function set(key, data) {
    const storageData = {
      data,
      expire: expire !== null ? new Date().getTime() + expire * 1000 : null,
    };

    const json = JSON.stringify(storageData);
    window.localStorage.setItem(key, json);
  }

  // 根据key获取localStorage中的数据
  function get(key) {
    // 获取localStorage中的数据
    const json = window.localStorage.getItem(key);
    if (json) {
      // 声明一个变量来存储解析后的数据
      let storageData = null;

      try {
        // 尝试解析json数据
        storageData = JSON.parse(json);
      } catch {
        // Prevent failure
      }

      if (storageData) {
        const { data, expire } = storageData;
        if (expire === null || expire >= Date.now()) return data;
      }

      remove(key);
      return null;
    }
  }

  function remove(key) {
    window.localStorage.removeItem(key);
  }

  function clear() {
    window.localStorage.clear();
  }

  return { set, get, remove, clear };
}

export const ls = createLocalStorage();

export const ss = createLocalStorage({ expire: null });

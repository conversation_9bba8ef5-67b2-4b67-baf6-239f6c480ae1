import { defineStore } from 'pinia'
import Cookies from 'js-cookie'
import zh from '@/lang/zh'
import en from '@/lang/en'

const useLanguageStore = defineStore('language', {
  state: () => ({
    language: Cookies.get('language') || 'zh',
    messages: {
      zh,
      en
    }
  }),
  getters: {
    currentLanguage: (state) => state.language,
    currentMessages: (state) => state.messages[state.language]
  },
  actions: {
    setLanguage(lang) {
      if (this.language !== lang) {
      this.language = lang
      Cookies.set('language', lang)
        // Force reactivity update
        this.$patch((state) => {
          state.language = lang
        })
        // Trigger a custom event for components to react to language changes
        window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }))
        // Force reload messages
        this.messages = {
          zh,
          en
        }
      }
    },
    getMessage(key) {
      const keys = key.split('.')
      let result = this.currentMessages
      for (const k of keys) {
        if (result[k]) {
          result = result[k]
        } else {
          return key
        }
      }
      return result
    }
  },
  persist: true
})

export default useLanguageStore 
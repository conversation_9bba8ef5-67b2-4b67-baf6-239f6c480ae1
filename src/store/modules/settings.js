import defaultSettings from "@/settings";
import { useDynamicTitle } from "@/utils/dynamicTitle";
import { getSystemInfo } from "@/api/system/setting";
const {
  sideTheme,
  showSettings,
  topNav,
  tagsView,
  fixedHeader,
  sidebarLogo,
  dynamicTitle,
} = defaultSettings;

const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";

const useSettingsStore = defineStore("settings", {
  state: () => ({
    title: "",
    theme: storageSetting.theme || "#409EFF",
    sideTheme: storageSetting.sideTheme || sideTheme,
    showSettings: showSettings,
    topNav:
      storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
    tagsView:
      storageSetting.tagsView === undefined
        ? tagsView
        : storageSetting.tagsView,
    fixedHeader:
      storageSetting.fixedHeader === undefined
        ? fixedHeader
        : storageSetting.fixedHeader,
    sidebarLogo:
      storageSetting.sidebarLogo === undefined
        ? sidebarLogo
        : storageSetting.sidebarLogo,
    dynamicTitle:
      storageSetting.dynamicTitle === undefined
        ? dynamicTitle
        : storageSetting.dynamicTitle,
    logoUrl: "",
    bigTitle: "",
    smallTitle: "",
  }),
  actions: {
    // 修改布局设置
    changeSetting(data) {
      const { key, value } = data;
      if (this.hasOwnProperty(key)) {
        this[key] = value;
      }
    },
    // 设置网页标题
    setTitle(title) {
      this.title = title;
      useDynamicTitle();
    },

    // 获取系统信息
    getSystemData() {
      return new Promise((resolve, reject) => {
        getSystemInfo()
          .then((res) => {
            this.logoUrl = `${window.location.protocol}//${window.location.host}/file/${res.data.logoUrl}`;
            this.bigTitle = res.data.bigTitle;
            this.smallTitle = res.data.smallTitle;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
  persist: true,
});

export default useSettingsStore;

import request from "@/utils/request";

// 执行优化参数分析

export function morisOptimizeResult(data) {
  return request({
    url: "/organism/morisInfo/moris-optimize/result",
    method: "post",
    data: data,
  });
}

// 查询当前用户是否有正在执行的任务
export function queryTaskStatus(query) {
  return request({
    url: "/organism/morisInfo/current-task-status",
    method: "get",
    params: query,
  });
}
// 当前任务结果数据查询
export function queryUserTaskList(query) {
  return request({
    url: "/organism/morisInfo/current-user-task",
    method: "get",
    params: query,
  });
}

// 执行常规分析
export function queryRunTask(data) {
  return request({
    url: "/organism/morisInfo/moris/result",
    method: "post",
    data: data,
  });
}

// 模板下载
export function templateDownload(data) {
  return request({
    url: "/organism/morisInfo/template/download",
    method: "get",
    params: data,
  });
}

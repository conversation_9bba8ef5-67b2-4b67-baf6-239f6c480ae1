import request from "@/utils/request";

// 查询测试单表列表
export function listSpecies(query) {
  return request({
    url: "/organism/speciesGis/list",
    method: "get",
    params: query,
  });
}

// 自定义分页接口
export function pageSpecies(query) {
  return request({
    url: "/organism/speciesGis/list",
    method: "get",
    params: query,
  });
}

// 查询测试单表详细
export function getSpecies(id) {
  return request({
    url: `/organism/speciesGis/${id}`,
    method: "get",
  });
}

// 新增测试单表
export function addSpecies(data) {
  return request({
    url: "/organism/speciesGis",
    method: "post",
    data: data,
  });
}

// 修改测试单表
export function updateSpecies(data) {
  return request({
    url: "/organism/speciesGis",
    method: "put",
    data: data,
  });
}

// 删除测试单表
export function delSpecies(data) {
  return request({
    url: "/organism/speciesGis",
    method: "delete",
    data: data,
  });
}

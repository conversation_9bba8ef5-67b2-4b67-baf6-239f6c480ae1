import request from "@/utils/request";

// 中心节点分析
export const analysisInfoCentral = (data) => {
  return request({
    url: "/organism/analysisInfo/centrality",
    method: "post",
    data: data,
  });
};
// 聚类分析
export const analysisInfoCluster = (data) => {
  return request({
    url: "/organism/analysisInfo/clustering",
    method: "post",
    data: data,
  });
};
// 脆弱性分析
export const analysisInfoWeakness = (data) => {
  return request({
    url: "/organism/analysisInfo/vulnerability",
    method: "post",
    data: data,
  });
};
// 分析结果预览链接
export const analysisInfoPreviewResult = (data) => {
  return request({
    url: "/organism/analysisInfo/vulnerability/result",
    method: "post",
    data: data,
  });
};

// 下载报告
export function downloadReport(query) {
  return request({
    url: "/organism/analysisInfo/batchDownload",
    method: "get",
    params: query,
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    responseType: "blob",
    timeout: 120000,
  });
}

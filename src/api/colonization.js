import request from "@/utils/request";

/**
 * 第一步：获取发生数据
 * @param {*} query
 * @returns
 */
// 从GBIF数据库获取发生数据并解析数据后返回
export function queryGenerationData(query) {
  return request({
    url: "/system/generationData/query/data",
    method: "get",
    params: query,
  });
}

// 移除某个发生数据excel文件
export function removeGenerationFile(data) {
  return request({
    url: "/system/generationData/data/upload/remove",
    method: "post",
    params: data,
  });
}

// 分页查询结果
export function generationDataResultList(data) {
  return request({
    url: "/system/generationData/data/page",
    method: "post",
    params: data,
  });
}

// 修改生成数据查询结果
export function generationDataUpdate(data) {
  return request({
    url: "/system/generationData",
    method: "put",
    data: data,
  });
}

// 删除生成数据查询结果
export function generationDataDelete(ids) {
  return request({
    url: `/system/generationData/${ids}`,
    method: "delete",
  });
}

// 发生数据优化
export function generationDataOptimize(data) {
  return request({
    url: "/system/generationData/optimize",
    method: "post",
    data: data,
  });
}

// 发生数据优化结果
export function generationDataList(data) {
  return request({
    url: "/system/generationData/data/page ",
    method: "post",
    params: data,
  });
}

/**
 * 第二步：获取环境数据
 * @param {*} query
 * @returns
 */

// 获取环境数据 同步
export function envDataLocal(data) {
  return request({
    url: "/system/envData/local",
    method: "post",
    data: data,
  });
}

export function queryEnvFileTree(query) {
  return request({
    url: "/system/envFile/tree",
    method: "get",
    params: query,
  });
}
// 移除文件
export function removeFile(url, data) {
  return request({
    url: url,
    method: "post",
    params: data,
  });
}

// 环境数据优化
export function envDataOptimize(data) {
  return request({
    url: "/system/envData/optimizeModernData",
    method: "post",
    data: data,
  });
}

// 环境数据优化结果
export function envDataOptimizeResult(query) {
  return request({
    url: "/system/envData/optimizeResult",
    method: "get",
    params: query,
  });
}

/**
 * 第三步：建模分析
 * @param {*} query
 * @returns
 */

// 回归任务执行
export function regressionTaskExecute(data) {
  return request({
    url: "/organism/algorithmInfo/regressor",
    method: "post",
    data: data,
  });
}

// 分类任务执行
export function classifierList(data) {
  return request({
    url: "/organism/algorithmInfo/classifier",
    method: "post",
    data: data,
  });
}

// 下载模板
export function downloadTemplate(data) {
  return request({
    url: "/system/organism/algorithmInfo/template/download",
    method: "get",
    params: data,
  });
}

// 查询当前用户是否有正在执行的回归任务
export function regressorTaskStatus(data) {
  return request({
    url: "/organism/algorithmInfo/regressor-task-status",
    method: "get",
    params: data,
  });
}

// 查询当前用户是否有正在执行的分类任务
export function classifierTaskStatus(data) {
  return request({
    url: "/organism/algorithmInfo/classifier-task-status",
    method: "get",
    params: data,
  });
}

// 任务结果图片查看regressor:回归任务 classifier:分类任务
export function regressorTaskResult(data) {
  return request({
    url: "/organism/algorithmInfo/regressor-user-task",
    method: "get",
    params: data,
  });
}

// 任务结果图片批量下载
export function batchDownload(data) {
  return request({
    url: "/organism/algorithmInfo/batchDownload",
    method: "get",
    params: data,
    timeout: 120000, // 设置超时时间
  });
}

// 移除文件
export function removeCommonFile(ids) {
  return request({
    url: `/organism/commonFile/${ids}`,
    method: "delete",
  });
}

// 定殖适生 建模分析 第三种模型
export function biomodeling(data) {
  return request({
    url: "/organism/algorithmInfo/biomod",
    method: "post",
    data: data,
  });
}

// 定殖适生 建模分析 第三种模型 下载
export function biomodelingDownload(data) {
  return request({
    url: "/organism/algorithmInfo/biomodDownload",
    method: "get",
    params: data,
  });
}

// 定殖适生 建模分析 第三种模型 任务状态
export function biomodelingTaskStatus(data) {
  return request({
    url: "/organism/algorithmInfo/biomod-task-status",
    method: "get",
    params: data,
  });
}

// 查询任务进度,返回值如果是false说明任务失败了，返回数值就是进度0-100
export function biomodelingTaskProgress(id) {
  return request({
    url: `/organism/algorithmInfo/task-progress?id=${id}`,
    method: "get",
  });
}

/**
 * 获取发生接口问题列表：
 * TODO
 * 1、分页查询结果和查询生成数据查询结果列表的区别？分别在哪儿调用
 */

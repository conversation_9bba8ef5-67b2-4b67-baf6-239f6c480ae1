import request from "@/utils/request";

export function getAppId() {
  return request({
    url: "/api/appId",
    method: "get",
  });
}
export function createChatHis(data) {
  return request({
    url: "/api/core/chat/init",
    method: "get",
    params: {
      ...data,
    },
  });
}
//获取历史对话列表
export function getChatHisList(data) {
  return request({
    url: "/api/core/chat/getHistories",
    method: "post",
    data,
  });
}
//删除历史对话列表
export function removeChatHis(data) {
  return request({
    url: "/api/core/chat/delHistory",
    method: "delete",
    params: {
      ...data,
    },
  });
}

//删除所有历史对话
export function removeAllHisList(data) {
  return request({
    url: "/api/core/chat/clearHistories",
    method: "delete",
    params: {
      ...data,
    },
  });
}

//更新历史对话信息：置顶，标题
export function updateHisChatInfo(data) {
  return request({
    url: "/api/core/chat/updateHistory",
    method: "post",
    data: data,
  });
}
//查看对话的具体聊天记录
export function getHisChatMessageList(data) {
  return request({
    url: "/api/core/chat/getPaginationRecords",
    method: "post",
    data: data,
  });
}
//删除聊天信息
export function deleteHisChatMessage(data) {
  return request({
    url: "/api/core/chat/item/delete",
    method: "delete",
    params: {
      ...data,
    },
  });
}

//打开源文件
export function openOriginFile(data) {
  return request({
    url: "/api/core/chat/dataset/collection/read",
    method: "post",
    data: data,
  });
}
//知识库搜索
export function getQuote(data) {
  return request({
    url: "/api/core/chat/quote/getQuote",
    method: "post",
    data: data,
  });
}
//获取单个对话记录运行详情
export function getChatDetail(data) {
  return request({
    url: "/api/core/chat/getResData",
    method: "get",
    params: {
      ...data,
    },
  });
}

//查看数据
export function viewDat(data) {
  return request({
    url: "/api/core/chat/dataset/collection/export",
    method: "post",
    data: data,
  });
}

//查看引用类型
export function getCollectionQuote(data) {
  return request({
    url: "/api/core/chat/quote/getCollectionQuote",
    method: "post",
    data: data,
  });
}
//点赞,取消点赞
export function approvalFeedback(data) {
  return request({
    url: "/api/core/chat/feedback/updateUserFeedback",
    method: "post",
    data: data,
  });
}

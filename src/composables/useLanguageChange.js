import { onMounted, onBeforeUnmount, getCurrentInstance } from "vue";

export function useLanguageChange() {
  const { proxy } = getCurrentInstance();

  const handleLanguageChange = () => {
    // Force component to re-render
    proxy.$forceUpdate();
  };

  onMounted(() => {
    window.addEventListener("languageChanged", handleLanguageChange);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("languageChanged", handleLanguageChange);
  });
}

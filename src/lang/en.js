export default {
  common: {
    confirm: "Confirm",
    cancel: "Cancel",
    close: "Close",
    reset: "Reset",
    search: "Search",
    refresh: "Refresh",
    save: "Save",
    close: "Close",
    save: "Save",
    delete: "Delete",
    edit: "Edit",
    search: "Search",
    reset: "Reset",
    loading: "Loading...",
    success: "Operation successful",
    error: "Operation failed",
    warning: "Warning",
    info: "Info",
    add: "Add",
    import: "Import",
    export: "Export",
    operation: "Operation",
    close: "Close",
    status: "Status",
    yes: "Yes",
    no: "No",
  },
  // 系统设置模块 START
  menu: {
    dashboard: "Dashboard",
    system: "System",
    user: "User",
    role: "Role",
    menu: "Menu",
    dict: "Dict",
    config: "Config",
    log: "Log",
    login: "Login Log",
    operlog: "Operation Log",
    menuName: "Menu Name",
    menuNamePlaceholder: "Please enter menu name",
    menuNameEng: "English Name",
    menuNameEngPlaceholder: "Please enter English name",
    status: "Status",
    statusPlaceholder: "Please select menu status",
    icon: "Icon",
    iconPlaceholder: "Please select icon",
    orderNum: "Order",
    orderNumPlaceholder: "Please enter order",
    perms: "Permission",
    permsPlaceholder: "Please enter permission",
    component: "Component Path",
    componentPlaceholder: "Please enter component path",
    createTime: "Create Time",
    parentMenu: "Parent Menu",
    parentMenuPlaceholder: "Please select parent menu",
    menuType: "Menu Type",

    typeDirectory: "Directory",
    typeMenu: "Menu",
    typeButton: "Button",
    isFrame: "Is External",
    isFrameTip: "If selected, the route address must start with `http(s)://`",
    path: "Route Path",
    pathPlaceholder: "Please enter route path",
    queryParam: "Route Param",
    queryParamPlaceholder: "Please enter route param",
    isCache: "Cache",
    cache: "Cache",
    noCache: "No Cache",
    visible: "Visible",
    visiblePlaceholder: "Please select visible status",
    expandCollapse: "Expand/Collapse",
    addTitle: "Add Menu",
    editTitle: "Edit Menu",
    deleteConfirm: 'Are you sure to delete the item named "{menuName}"?',
    addSuccess: "Add successful",
    editSuccess: "Edit successful",
    deleteSuccess: "Delete successful",
    menuNameRequired: "Menu name is required",
    orderNumRequired: "Order is required",
    pathRequired: "Route path is required",
    iconRequired: "Icon is required",
    componentRequired: "Component path is required",
    permsRequired: "Permission is required",
    parentMenuRequired: "Parent menu is required",
    pathTip:
      "Route address to access, e.g.: `user`. For external links, start with `http(s)://`",
    componentTip:
      "Component path to access, e.g.: `system/user/index`, default in `views` directory",
    permsTip:
      "Permission character defined in controller, e.g.: @SaCheckPermission('system:user:list')",
    queryParamTip:
      'Default parameters for route access, e.g.: `{"id": 1, "name": "ry"}`',
    isCacheTip:
      "If selected, it will be cached by `keep-alive`, component `name` must match the route",
    visibleTip:
      "If hidden, the route will not appear in sidebar but can still be accessed",
    statusTip:
      "If disabled, the route will not appear in sidebar and cannot be accessed",
    menuTypeRequired: "Menu type is required",
    statusRequired: "Menu status is required",
    visibleRequired: "Visible status is required",
    isFrameRequired: "External status is required",
    isCacheRequired: "Cache status is required",
    queryParamRequired: "Route param is required",
    tooltipPath:
      "The route address to access, e.g.: `user`. If it is an external address, it must start with `http(s)://`",
    tooltipComponent:
      "The component path to access, e.g.: `system/user/index`, default under `views` directory",
    tooltipPerms:
      "Permission string defined in the controller, e.g.: @SaCheckPermission('system:user:list')",
    tooltipQueryParam:
      'Default parameters passed to the route, e.g.: `{"id": 1, "name": "ry"}`',
    tooltipIsCache:
      "If selected, it will be cached by `keep-alive`, and the component `name` and address must match",
    tooltipVisible:
      "If hidden, the route will not appear in the sidebar but can still be accessed",
    tooltipStatus:
      "If disabled, the route will not appear in the sidebar and cannot be accessed",
  },
  role: {
    roleId: "Role ID",
    roleName: "Role Name",
    roleNamePlaceholder: "Please enter role name",
    roleKey: "Permission Character",
    roleKeyPlaceholder: "Please enter permission character",
    roleSort: "Display Order",
    roleSortPlaceholder: "Please enter display order",
    status: "Status",
    statusPlaceholder: "Role status",
    createTime: "Create Time",
    startDate: "Start Date",
    endDate: "End Date",
    assignMenu: "Assign Menu",
    assignData: "Assign Data Permission",
    menuPermission: "Menu Permission",
    expandAll: "Expand/Collapse",
    selectAll: "Select All/Cancel All",
    parentChild: "Parent-Child Linkage",
    remark: "Remark",
    remarkPlaceholder: "Please enter remark",
    dataScope: "Data Permission",
    dataScopePlaceholder: "Please select data permission",
    deptPermission: "Department Permission",
    roleNamePlaceholder: "Please enter role name",
    roleKeyPlaceholder: "Please enter permission character",
    roleSortPlaceholder: "Please enter display order",
    statusPlaceholder: "Please select role status",
    dataScopePlaceholder: "Please select data permission",
    menuPermissionPlaceholder: "Please select menu permission",
    deptPermissionPlaceholder: "Please select department permission",
    remarkPlaceholder: "Please enter remark",
    deleteConfirm: 'Are you sure to delete the role with ID "{roleIds}"?',
    exportConfirm: "Are you sure to export all role data?",
    statusChangeConfirm:
      'Are you sure to change the status of role "{roleName}"?',
    statusChangeSuccess: "Status changed successfully",
    deleteSuccess: "Delete successful",
    exportSuccess: "Export successful",
    addSuccess: "Add successful",
    editSuccess: "Edit successful",
    assignMenuSuccess: "Menu assigned successfully",
    assignDataSuccess: "Data permission assigned successfully",
  },

  user: {
    treeDeptPlaceholder: "Please enter deptname",
    username: "Username",
    usernamePlaceholder: "Please enter username",
    phonenumber: "Phone Number",
    phonenumberPlaceholder: "Please enter phone number",
    status: "Status",
    statusPlaceholder: "User status",
    createTime: "Create Time",
    startDate: "Start Date",
    endDate: "End Date",
    userId: "User ID",
    userName: "Username",
    userNamePlaceholder: "Please enter username",
    nickName: "Nickname",
    nickNamePlaceholder: "Please enter nickname",
    dept: "Department",
    deptPlaceholder: "Please select department",
    email: "Email",
    emailPlaceholder: "Please enter email",
    password: "Password",
    passwordPlaceholder: "Please enter password",
    sex: "Gender",
    sexPlaceholder: "Please select gender",
    role: "Role",
    rolePlaceholder: "Please select role",
    remark: "Remark",
    remarkPlaceholder: "Please enter remark",
    resetPwd: "Reset Password",
    assignRole: "Assign Role",
    importTitle: "Import Users",
    importText: "Drag file here, or",
    importClick: "click to upload",
    importTip: "Update existing user data",
    importTemplate: "Download Template",
  },

  toolbar: {
    showSearch: "Show Search",
    hideSearch: "Hide Search",
    refresh: "Refresh",
    showHideColumns: "Show/Hide Columns",
    showHideTitle: "Show/Hide",
    show: "Show",
    hide: "Hide",
  },
  operlog: {
    operId: "Log ID",
    title: "System Module",
    businessType: "Operation Type",
    requestMethod: "Request Method",
    operName: "Operator",
    deptName: "Department",
    operIp: "Operation IP",
    operLocation: "Operation Location",
    status: "Operation Status",
    operTime: "Operation Time",
    costTime: "Cost Time",
    detail: "Detail",
    detailTitle: "Operation Log Detail",
    loginInfo: "Login Info",
    requestInfo: "Request Info",
    operModule: "Operation Module",
    operMethod: "Operation Method",
    operParam: "Request Parameters",
    jsonResult: "Response Parameters",
    normal: "Normal",
    fail: "Failed",
    errorMsg: "Error Message",
    millisecond: "ms",
    deleteConfirm: 'Are you sure to delete the log with ID "{operIds}"?',
    cleanConfirm: "Are you sure to clear all operation logs?",
    cleanSuccess: "Clear successful",
    operIpPlaceholder: "Please enter operation IP",
    titlePlaceholder: "Please enter system module",
    operNamePlaceholder: "Please enter operator",
    businessTypePlaceholder: "Operation type",
    statusPlaceholder: "Operation status",
    startDate: "Start Date",
    endDate: "End Date",
    clean: "Clear",
  },
  logininfor: {
    loginId: "Login ID",
    loginName: "Login Name",
    loginNamePlaceholder: "Please enter login name",
    status: "Status",
    statusPlaceholder: "Login status",
    startDate: "Start Date",
    endDate: "End Date",
    clean: "Clear",
    deleteConfirm: 'Are you sure to delete the login with ID "{loginIds}"?',
    cleanConfirm: "Are you sure to clear all login information?",
    loginNamePlaceholder: "Please enter login name",
    statusPlaceholder: "Login status",
  },
  dept: {
    deptId: "Dept ID",
    deptName: "Dept Name",
    deptNamePlaceholder: "Please enter dept name",
    deptNameEng: "English Name",
    deptNameEngPlaceholder: "Please enter English name",
    status: "Status",
    email: "Email",
    emailPlaceholder: "Please enter email",
  },
  notice: {
    noticeId: "Notice ID",
    noticeTitle: "Notice Title",
    noticeTitlePlaceholder: "Please enter notice title",
    noticeType: "Notice Type",
    noticeTypePlaceholder: "Please select notice type",
    status: "Status",
    createBy: "Creator",
    createByPlaceholder: "Please enter creator",
    createTime: "Create Time",
    content: "Content",
  },
  post: {
    postId: "Post ID",
    postName: "Post Name",
    postNamePlaceholder: "Please enter post name",
    postCode: "Post Code",
    postCodePlaceholder: "Please enter post code",
    postSort: "Post Sort",
    postSortPlaceholder: "Please enter post sort",
    status: "Status",
    statusPlaceholder: "Please select post status",
    createTime: "Create Time",
    remark: "Remark",
    remarkPlaceholder: "Please enter remark",
  },
  dict: {
    dictId: "Dict ID",
    dictName: "Dict Name",
    dictNamePlaceholder: "Please enter dict name",
    dictType: "Dict Type",
    dictTypePlaceholder: "Please enter dict type",
    status: "Status",
    statusPlaceholder: "Please select dict status",
    createTime: "Create Time",
    remark: "Remark",
    remarkPlaceholder: "Please enter remark",
    dictNameRequired: "Dict name is required",
    dictTypeRequired: "Dict type is required",
  },
  config: {
    configId: "Config ID",
    configName: "Config Name",
    configNamePlaceholder: "Please enter config name",
    configKey: "Config Key",
    configKeyPlaceholder: "Please enter config key",
    configValue: "Config Value",
    configValuePlaceholder: "Please enter config value",
    configType: "Config Type",
    configTypePlaceholder: "Please select config type",
    createTime: "Create Time",
    remark: "Remark",
    remarkPlaceholder: "Please enter remark",
    configNameRequired: "Config name is required",
    configKeyRequired: "Config key is required",
    configValueRequired: "Config value is required",
    refreshCache: "Refresh Cache",
  },
  oss: {
    ossId: "OSS ID",
    fileName: "File Name",
    fileUrl: "File URL",
    createTime: "Create Time",
    download: "Download",
    originalName: "Original Name",
    fileSuffix: "File Suffix",
    fileSize: "File Size",
    fileType: "File Type",
    fileTypePlaceholder: "Please select file type",
    fileSizePlaceholder: "Please enter file size",
    fileSuffixPlaceholder: "Please enter file suffix",
    fileTypePlaceholder: "Please select file type",
    createTime: "Create Time",
    download: "Download",
    createBy: "Creator",
    service: "Service",
    fileNamePlaceholder: "Please enter file name",
    fileUrlPlaceholder: "Please enter file URL",
    createTimePlaceholder: "Please select create time",
    startDate: "Start Date",
    endDate: "End Date",
    clean: "Clear",
  },
  setting: {
    systemLogo: "System Logo",
    systemNameZh: "System Name (Chinese)",
    systemNameZhPlaceholder: "Please enter system name in Chinese",
    systemNameEn: "System Name (English)",
    systemNameEnPlaceholder: "Please enter system name in English",
    systemNameZhRequired: "System name in Chinese is required",
    getSystemInfoFailed: "Failed to get system info",
    saveSystemInfoSuccess: "System info saved successfully",
    saveSystemInfoFailed: "Failed to save system info",
  },
  // 系统设置模块 END
  map: {
    title: "Map",
    cluster: "Point Cluster",
    raster: "Raster Layer",
    language: "Language",
    chinese: "Chinese",
    english: "English",
  },
  colonization: {
    title: "Invasive System",
    algorithmModel: "Algorithm Model",
    getOccurrenceData: "Get Occurrence Data",
    getEnvironmentData: "Get Environment Data",
    modelingAnalysis: "Modeling Analysis",
    getway: "Acquisition Method",
    getwayPlaceholder: "Please select acquisition method",
    database: "Database",
    databasePlaceholder: "Please select database",
    fileName: "File Name",
    fileNamePlaceholder: "Please enter file name",
    speciesName: "Species Name",
    speciesNamePlaceholder: "Please enter species name",
    dataAmount: "Data Amount",
    dataAmountPlaceholder: "Please enter data amount",
    uploadFile: "Upload File",
    uploadFileTip: "Drag xls, xlsx files here, or click to upload",
    nowWeatherFiles: "Modern Climate Data",
    tomWeatherFiles: "Future Climate Data",
    futureWeatherFiles: "Future Climate Data",
    nowWeatherFilesPlaceholder: "Please select modern climate data",
    futureWeatherFilesPlaceholder: "Please select future climate data",
    modelAlgorithm: "Model Algorithm",
    modelAlgorithmPlaceholder: "Please select model algorithm",

    // biomod related translations
    biomodSpeciesName: "Species Name",
    biomodSpeciesNamePlaceholder:
      "Please enter species name, consistent with the occurrence data file",
    pseudoAbsenceRep: "Pseudo-absence Repetitions",
    pseudoAbsenceRepPlaceholder: "Please enter pseudo-absence repetitions",
    pseudoAbsenceNum: "Number of Pseudo-absences",
    pseudoAbsenceNumPlaceholder: "Please enter number of pseudo-absences",
    pseudoAbsenceStrategy: "Pseudo-absence Strategy",
    pseudoAbsenceStrategyPlaceholder: "Please select pseudo-absence strategy",
    modelName: "Model Name",
    modelNamePlaceholder: "Please enter model name",
    minDistance: "Minimum Distance",
    minDistancePlaceholder: "Please enter minimum distance",
    maxDistance: "Maximum Distance",
    maxDistancePlaceholder: "Please enter maximum distance",
    modelValidationMethod: "Model Validation Method",
    modelValidationMethodPlaceholder: "Please select model validation method",
    modelAlgorithms: "Model Algorithms",
    modelAlgorithmsPlaceholder: "Please select model algorithms",
    modelRepetitions: "Model Repetitions",
    modelRepetitionsPlaceholder: "Please enter model repetitions",
    trainingSetRatio: "Training Set Ratio",
    trainingSetRatioPlaceholder: "Please enter training set ratio",
    kFoldValidation: "K-Fold Cross Validation",
    kFoldValidationPlaceholder: "Please enter K-fold cross validation",
    modelEvaluationMetrics: "Model Evaluation Metrics",
    modelEvaluationMetricsPlaceholder: "Please select model evaluation metrics",
    ensembleMethods: "Ensemble Methods",
    ensembleMethodsPlaceholder: "Please select ensemble methods",
    modelSelectionMetrics: "Model Selection Metrics",
    modelSelectionMetricsPlaceholder: "Please select model selection metrics",
    environmentDataMetrics: "Environment Data Metrics",
    environmentDataMetricsPlaceholder: "Please select environment data metrics",
    selectionThreshold: "Selection Threshold",
    selectionThresholdPlaceholder: "Please enter reasonable threshold",

    // Buttons and operations
    sync: "Sync",
    reset: "Reset",
    optimize: "Optimize",
    viewOptimizationResults: "View Optimization Results",
    algorithmCall: "Algorithm Call",
    algorithmProgress: "Algorithm Progress",
    refresh: "Refresh",
    downloadResults: "Download Results",
    noData: "No Data",
    sparsificationDistance: "Sparsification Distance (km)",
    sparsificationDistancePlaceholder:
      "Please enter sparsification distance (unit: km)",
    repeatRunTimes: "Repeat Run Times",
    repeatRunTimesPlaceholder: "Please enter repeat run times",
    correlationCoefficient: "Correlation Coefficient",
    correlationCoefficientPlaceholder: "Please enter correlation coefficient",

    // Dialog titles
    occurrenceDataOptimizationParams: "Occurrence Data Optimization Parameters",
    environmentDataOptimizationParams:
      "Environment Data Optimization Parameters",
    environmentDataOptimizationResults: "Environment Data Optimization Results",

    // Table related
    exportOccurrenceData: "Export Occurrence Data",
    exportColonizationData: "Export Colonization Data",
    speciesName: "Species Name",
    longitude: "Longitude",
    latitude: "Latitude",
    modifiedTime: "Modified Time",

    // Tips
    dataInfo: "Data Information",
    resultPreview: "Result Preview",

    // Regression and classification algorithm related translations
    isFinetune: "Fine-tune",
    trainingRatio: "Training Set Ratio",
    trainingRatioPlaceholder: "Please enter training set ratio 0~1",
    pretrainFile: "Pretrain File",
    predictFile: "Predict File",
    finetuneFile: "Finetune File",

    // biomod related translations
    modelName: "Model Name",
    modelNamePlaceholder: "Please enter model name",
    minDistance: "Minimum Distance",
    minDistancePlaceholder: "Please enter minimum distance",
    maxDistance: "Maximum Distance",
    maxDistancePlaceholder: "Please enter maximum distance",
    modelValidationMethod: "Model Validation Method",
    modelValidationMethodPlaceholder: "Please select model validation method",
    modelAlgorithms: "Model Algorithms",
    modelAlgorithmsPlaceholder: "Please select model algorithms",
    modelRepetitions: "Model Repetitions",
    modelRepetitionsPlaceholder: "Please enter model repetitions",
    trainingSetRatio: "Training Set Ratio",
    trainingSetRatioPlaceholder: "Please enter training set ratio",
    kFoldValidation: "K-Fold Validation",
    kFoldValidationPlaceholder: "Please enter K-fold validation",
    modelEvaluationMetrics: "Model Evaluation Metrics",
    modelEvaluationMetricsPlaceholder: "Please select model evaluation metrics",
    ensembleMethods: "Ensemble Methods",
    ensembleMethodsPlaceholder: "Please select ensemble methods",
    modelSelectionMetrics: "Model Selection Metrics",
    modelSelectionMetricsPlaceholder: "Please select model selection metrics",
    environmentDataMetrics: "Environment Data Metrics",
    environmentDataMetricsPlaceholder: "Please select environment data metrics",
    selectionThresholdPlaceholder: "Please enter threshold reasonably",
    pseudoAbsenceStrategyPlaceholder: "Please select pseudo-absence strategy",

    // Form validation messages
    validation: {
      selectGetway: "Please select acquisition method",
      selectDatabase: "Please select database",
      enterSpeciesName: "Please enter species name",
      enterDataAmount: "Please enter data amount",
      uploadOccurrenceFile: "Please upload occurrence data file",
      selectModernClimate: "Please select or upload modern climate data",
      selectFutureClimate: "Please select or upload future climate data",
      selectFutureWeather: "Please select future weather data",
      selectAlgorithm: "Please select algorithm",
      enterTrainingRatio: "Please enter training ratio",
      uploadPretrainFile: "Please upload pretrain file",
      uploadPredictFile: "Please upload predict file",
      uploadFinetuneFile: "Please upload finetune file",
      enterSpeciesNameBiomod: "Please enter species name",
      enterPseudoAbsenceRep: "Please enter pseudo-absence repetitions",
      enterPseudoAbsenceNum: "Please enter number of pseudo-absences",
      selectPseudoAbsenceStrategy: "Please select pseudo-absence strategy",
      enterModelName: "Please enter model name",
      enterMinDistance: "Please enter minimum distance",
      enterMaxDistance: "Please enter maximum distance",
      selectModelValidation: "Please select model validation method",
      selectModelAlgorithms: "Please select model algorithms",
      enterModelRepetitions: "Please enter model repetitions",
      enterTrainingSetRatio: "Please enter training set ratio",
      enterKFoldValidation: "Please enter K-fold validation",
      selectModelEvaluation: "Please select model evaluation metrics",
      selectEnsembleMethods: "Please select ensemble methods",
      selectModelSelection: "Please select model selection metrics",
      selectEnvironmentData: "Please select environment data metrics",
      enterSelectionThreshold: "Please enter selection threshold",
      enterThinPar: "Please enter sparsification distance parameter",
      enterReps: "Please enter repeat run times parameter",
      enterCorrelationCoefficient: "Please enter correlation coefficient",
    },
  },

  // File upload related translations
  upload: {
    selectFile: "Select File",
    pleaseUpload: "Please upload",
    sizeLimit: "size not exceeding",
    formatLimit: "format as",
    fileText: "files",
    downloadTemplate: "Download Template",
    delete: "Delete",
    formatError:
      "File format is incorrect, please upload {formats} format files!",
    sizeError: "Upload file size cannot exceed {size} MB!",
    limitError: "Number of uploaded files cannot exceed {limit}!",
    uploadFailed: "File upload failed",
    uploading: "Uploading file, please wait...",
  },

  // Spread analysis related translations
  spread: {
    modelAlgorithm: "Model Algorithm",
    basicSettings: "Basic Settings",
    modelParameters: "Model Parameters",
    randomSeed: "Random Seed",
    randomSeedPlaceholder: "Please enter random seed",
    inputDataType: "Input Data Type",
    inputDataTypePlaceholder: "Please select input data type",
    longitude: "Longitude",
    longitudePlaceholder: "Please enter longitude",
    latitude: "Latitude",
    latitudePlaceholder: "Please enter latitude",
    initialInvasionProbability: "Initial Invasion Probability",
    initialInvasionProbabilityPlaceholder:
      "Please enter initial invasion probability",
    simulationRepetitions: "Simulation Repetitions",
    simulationRepetitionsPlaceholder: "Please enter simulation repetitions",
    simulationIterations: "Simulation Iterations (Years)",
    simulationIterationsPlaceholder: "Please enter simulation iterations",
    optimizationFunction: "Optimization Function",
    optimizationFunctionPlaceholder: "Please select optimization function",
    optimize: "Optimize",
    resultPreview: "Result Preview",
    dataInfo: "Data Information",
    parameterOptimization: "Parameter Optimization",
    firstInvasionYear: "First Invasion Year",
    downloadExecutionResults: "Download Execution Results",
    execute: "Execute",
    jumpDiffusionFrequency: "Jump Diffusion Frequency",
    jumpDiffusionFrequencyPlaceholder: "Please enter jump diffusion frequency",
    jumpDiffusionDistanceDistribution: "Jump Diffusion Distance Distribution",
    jumpDiffusionDistanceDistributionPlaceholder:
      "Please select jump diffusion distance distribution",
    jumpDiffusionDistanceMean: "Jump Diffusion Distance Mean",
    jumpDiffusionDistanceMeanPlaceholder:
      "Please enter jump diffusion distance mean",
    jumpDiffusionGammaParameter: "Jump Diffusion Gamma Parameter",
    jumpDiffusionGammaParameterPlaceholder:
      "Please enter jump diffusion gamma parameter",
    humanActivityIndexWeighting: "Human Activity Index Weighting",
    humanActivityIndexWeightingPlaceholder:
      "Please select human activity index weighting",
    jumpDiffusionDistanceVariance: "Jump Diffusion Distance Variance",
    jumpDiffusionDistanceVariancePlaceholder:
      "Please enter jump diffusion distance variance",
    roadWeight: "Road Weight",
    roadWeightClass1: "Class I Road Weight",
    roadWeightClass1Placeholder: "Please enter Class I road weight",
    roadWeightClass2: "Class II Road Weight",
    roadWeightClass2Placeholder: "Please enter Class II road weight",
    roadWeightClass3: "Class III Road Weight",
    roadWeightClass3Placeholder: "Please enter Class III road weight",
    roadWeightClass4: "Class IV Road Weight",
    roadWeightClass4Placeholder: "Please enter Class IV road weight",
    roadWeightClass5: "Class V Road Weight",
    roadWeightClass5Placeholder: "Please enter Class V road weight",
    roadWeightClass6: "Class VI Road Weight",
    roadWeightClass6Placeholder: "Please enter Class VI road weight",
    minimumWeightBetweenCells: "Minimum Weight Between Cells",
    minimumWeightBetweenCellsPlaceholder:
      "Please enter minimum weight between cells",
    inputFiles: "Input Files",
    roadNetworkFile: "Road Network File",
    mapFile: "Map File",
    observationDataFile: "Observation Data File",
    parameterDataFile: "Parameter Data File",
  },

  // Risk analysis related translations
  risk: {
    riskNodeCentralityAnalysis: "Risk Node Centrality Analysis",
    clusteringResults: "Clustering Results",
    analysisResults: "Analysis Results",
    dragFileHere: "Drag xls, xlsx format files here, or ",
    clickToUpload: "click to upload",
    runDirectedNetwork: "Run Directed Network",
    runUndirectedNetwork: "Run Undirected Network",
    clusteringMethod: "Clustering Method",
    clusteringMethodPlaceholder: "Please select clustering method",
    riskAssessmentIndicators: "Risk Assessment Indicators",
    riskAssessmentIndicatorsPlaceholder:
      "Please select risk assessment indicators, at least two items",
    removalQuantityPerTime: "Removal Quantity Per Time",
    removalQuantityPerTimePlaceholder: "Please enter removal quantity per time",
    selectInterfaceLanguage: "Select Interface Language",
    analysisRun: "Analysis Run",
    downloadReport: "Download Report",
    startClustering: "Start Clustering",
    startAnalysis: "Start Analysis",
    exportResults: "Export Results",
    clickToScale: "Click to {action}",
    restore: "restore",
    enlarge: "enlarge",
    noAnalysisData: "No analysis data",
    resultPreview: "Result Preview",
    openPreviewInNewPage: "Open preview in new page",
    chinese: "Chinese",
    english: "English",
  },

  // Navbar related translations
  navbar: {
    welcome: "Welcome",
    personalCenter: "Personal Center",
    layoutSettings: "Layout Settings",
    logout: "Logout",
    chinese: "中文",
    english: "English",
    logoutConfirm: "Are you sure you want to log out and exit the system?",
    tip: "Tip",
  },

  // Profile related translations
  profile: {
    personalInfo: "Personal Information",
    username: "Username",
    phoneNumber: "Phone Number",
    email: "Email",
    department: "Department",
    role: "Role",
    createDate: "Create Date",
    basicInfo: "Basic Information",
    changePassword: "Change Password",
    nickname: "Nickname",
    gender: "Gender",
    male: "Male",
    female: "Female",
    nicknameRequired: "Nickname cannot be empty",
    emailRequired: "Email address cannot be empty",
    emailFormat: "Please enter a valid email address",
    phoneRequired: "Phone number cannot be empty",
    phoneFormat: "Please enter a valid phone number",
    updateSuccess: "Update successful",
    oldPassword: "Old Password",
    newPassword: "New Password",
    confirmPassword: "Confirm Password",
    oldPasswordPlaceholder: "Please enter old password",
    newPasswordPlaceholder: "Please enter new password",
    confirmPasswordPlaceholder: "Please confirm new password",
    oldPasswordRequired: "Old password cannot be empty",
    newPasswordRequired: "New password cannot be empty",
    confirmPasswordRequired: "Confirm password cannot be empty",
    passwordLength: "Length should be 6 to 20 characters",
    passwordMismatch: "The two passwords do not match",

    option1: "Option 1",
    option1Placeholder: "Please enter option 1",
    option2: "Option 2",
    option2Placeholder: "Please enter option 2",
    option3: "Option 3",
    option3Placeholder: "Please enter option 3",
    option4: "Option 4",
    option4Placeholder: "Please enter option 4",
    option5: "Option 5",
    nowWeatherFilesPlaceholder: "Please select modern climate data",
    futureWeatherFilesPlaceholder: "Please select future climate data",
    modelAlgorithm: "Model Algorithm",
    modelAlgorithmPlaceholder: "Please select model algorithm",
    option1: "Option 1",
    option1Placeholder: "Please enter option 1",
    option2: "Option 2",
    option2Placeholder: "Please enter option 2",
    option3: "Option 3",
    option3Placeholder: "Please enter option 3",
    option4: "Option 4",
    option4Placeholder: "Please enter option 4",
    option5: "Option 5",
    option5Placeholder: "Please enter option 5",
    option6: "Option 6",
    option6Placeholder: "Please enter option 6",
    sync: "Sync",
    algorithmCall: "Algorithm Call",
    parameterOptimization: "Parameter Optimization",
    optimizationOptions: "Optimization Options",
    taskStatus: "Task Status",
    taskList: "Task List",
    map: "Map",
    dataTable: "Data Table",
  },
  login: {
    title: "Invasive System Management Platform",
    username: "Username",
    usernamePlaceholder: "Please enter username",
    password: "Password",
    passwordPlaceholder: "Please enter password",
    captcha: "Captcha",
    captchaPlaceholder: "Please enter captcha",
    rememberMe: "Remember Me",
    login: "Login",
    register: "Register",
    forgetPassword: "Forget Password",
    loginSuccess: "Login successful",
    loginFailed: "Login failed",
    usernameRequired: "Username is required",
    passwordRequired: "Password is required",
    captchaRequired: "Captcha is required",
  },
  home: {
    uploadFile: "Upload File",
    uploadFileTip: "Drag xls, xlsx files here, or click to upload",
    runDirectedNetwork: "Run Directed Network",
    runUndirectedNetwork: "Run Undirected Network",
    clusteringMethod: "Clustering Method",
    clusteringMethodPlaceholder: "Please select clustering method",
    run: "Run",
    reset: "Reset",
    results: "Results",
    dataTable: "Data Table",
  },
};

export default {
  common: {
    confirm: "确认",
    cancel: "取消",
    close: "关闭",
    reset: "重置",
    search: "搜索",
    refresh: "刷新",
    save: "保存",
    close: "关闭",
    save: "保存",
    delete: "删除",
    edit: "编辑",
    search: "搜索",
    reset: "重置",
    loading: "加载中...",
    success: "操作成功",
    error: "操作失败",
    warning: "警告",
    info: "提示",
    add: "新增",
    import: "导入",
    export: "导出",
    operation: "操作",
    close: "关闭",
    status: "状态",
    yes: "是",
    no: "否",
  },
  // 系统设置模块 START
  menu: {
    dashboard: "首页",
    system: "系统管理",
    user: "用户管理",
    role: "角色管理",
    menu: "菜单管理",
    dict: "字典管理",
    config: "参数设置",
    log: "日志管理",
    login: "登录日志",
    operlog: "操作日志",
    menuName: "菜单名称",
    menuNamePlaceholder: "请输入菜单名称",
    menuNameEng: "英文名称",
    menuNameEngPlaceholder: "请输入英文名称",
    status: "状态",
    statusPlaceholder: "请选择菜单状态",
    icon: "图标",
    iconPlaceholder: "请选择图标",
    orderNum: "显示顺序",
    orderNumPlaceholder: "请输入显示顺序",
    perms: "权限标识",
    permsPlaceholder: "请输入权限标识",
    component: "组件路径",
    componentPlaceholder: "请输入组件路径",
    createTime: "创建时间",
    parentMenu: "上级菜单",
    parentMenuPlaceholder: "请选择上级菜单",
    menuType: "菜单类型",
    typeDirectory: "目录",
    typeMenu: "菜单",
    typeButton: "按钮",

    isFrame: "是否外链",
    isFrameTip: "选择是外链则路由地址需要以`http(s)://`开头",
    path: "路由地址",
    pathPlaceholder: "请输入路由地址",
    queryParam: "路由参数",
    queryParamPlaceholder: "请输入路由参数",
    isCache: "是否缓存",
    cache: "缓存",
    noCache: "不缓存",
    visible: "显示状态",
    visiblePlaceholder: "请选择显示状态",
    expandCollapse: "展开/折叠",
    addTitle: "新增菜单",
    editTitle: "修改菜单",
    deleteConfirm: '是否确认删除名称为"{menuName}"的数据项?',
    addSuccess: "新增成功",
    editSuccess: "修改成功",
    deleteSuccess: "删除成功",
    menuNameRequired: "菜单名称不能为空",
    orderNumRequired: "菜单顺序不能为空",
    pathRequired: "路由地址不能为空",
    iconRequired: "图标不能为空",
    componentRequired: "组件路径不能为空",
    permsRequired: "权限标识不能为空",
    parentMenuRequired: "上级菜单不能为空",
    pathTip:
      "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
    componentTip:
      "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
    permsTip:
      "控制器中定义的权限字符，如：@SaCheckPermission('system:user:list')",
    queryParamTip: '访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',
    isCacheTip:
      "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",
    visibleTip: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
    statusTip: "选择停用则路由将不会出现在侧边栏，也不能被访问",
    menuTypeRequired: "菜单类型不能为空",
    statusRequired: "菜单状态不能为空",
    visibleRequired: "显示状态不能为空",
    isFrameRequired: "是否外链不能为空",
    isCacheRequired: "是否缓存不能为空",
    queryParamRequired: "路由参数不能为空",
    tooltipPath:
      "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
    tooltipComponent:
      "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
    tooltipPerms:
      "控制器中定义的权限字符，如：@SaCheckPermission('system:user:list')",
    tooltipQueryParam: '访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',
    tooltipIsCache:
      "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",
    tooltipVisible: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
    tooltipStatus: "选择停用则路由将不会出现在侧边栏，也不能被访问",
  },
  role: {
    roleId: "角色编号",
    roleName: "角色名称",
    roleNamePlaceholder: "请输入角色名称",
    roleKey: "权限字符",
    roleKeyPlaceholder: "请输入权限字符",
    roleSort: "显示顺序",
    roleSortPlaceholder: "请输入显示顺序",
    status: "状态",
    statusPlaceholder: "角色状态",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    assignMenu: "分配菜单",
    assignData: "分配数据权限",
    menuPermission: "菜单权限",
    expandAll: "展开/折叠",
    selectAll: "全选/取消全选",
    parentChild: "父子联动",
    remark: "备注",
    remarkPlaceholder: "请输入备注",
    dataScope: "数据权限",
    dataScopePlaceholder: "请选择数据权限",
    deptPermission: "部门权限",
    roleNamePlaceholder: "请输入角色名称",
    roleKeyPlaceholder: "请输入权限字符",
    roleSortPlaceholder: "请输入显示顺序",
    statusPlaceholder: "请选择角色状态",
    dataScopePlaceholder: "请选择数据权限",
    menuPermissionPlaceholder: "请选择菜单权限",
    deptPermissionPlaceholder: "请选择部门权限",
    remarkPlaceholder: "请输入备注",
    deleteConfirm: '是否确认删除角色编号为"{roleIds}"的数据项?',
    exportConfirm: "是否确认导出所有角色数据项?",
    statusChangeConfirm: '是否确认修改角色"{roleName}"的状态?',
    statusChangeSuccess: "修改成功",
    deleteSuccess: "删除成功",
    exportSuccess: "导出成功",
    addSuccess: "新增成功",
    editSuccess: "修改成功",
    assignMenuSuccess: "分配菜单成功",
    assignDataSuccess: "分配数据权限成功",
  },

  user: {
    treeDeptPlaceholder: "请输入部门名称",
    username: "用户名称",
    usernamePlaceholder: "请输入用户名称",
    phonenumber: "手机号码",
    phonenumberPlaceholder: "请输入手机号码",
    status: "状态",
    statusPlaceholder: "用户状态",
    createTime: "创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    userId: "用户编号",
    userName: "用户名称",
    userNamePlaceholder: "请输入用户名称",
    nickName: "用户昵称",
    nickNamePlaceholder: "请输入用户昵称",
    dept: "部门",
    deptPlaceholder: "请选择归属部门",
    email: "邮箱",
    emailPlaceholder: "请输入邮箱",
    password: "密码",
    passwordPlaceholder: "请输入密码",
    sex: "性别",
    sexPlaceholder: "请选择性别",
    role: "角色",
    rolePlaceholder: "请选择角色",
    remark: "备注",
    remarkPlaceholder: "请输入备注",
    resetPwd: "重置密码",
    assignRole: "分配角色",
    importTitle: "用户导入",
    importText: "将文件拖到此处，或",
    importClick: "点击上传",
    importTip: "是否更新已经存在的用户数据",
    importTemplate: "下载模板",
  },

  toolbar: {
    showSearch: "显示搜索",
    hideSearch: "隐藏搜索",
    refresh: "刷新",
    showHideColumns: "显隐列",
    showHideTitle: "显示/隐藏",
    show: "显示",
    hide: "隐藏",
  },
  operlog: {
    operId: "日志编号",
    title: "系统模块",
    businessType: "操作类型",
    requestMethod: "请求方式",
    operName: "操作人员",
    deptName: "部门",
    operIp: "操作地址",
    operLocation: "操作地点",
    status: "操作状态",
    operTime: "操作时间",
    costTime: "消耗时间",
    detail: "详细",
    detailTitle: "操作日志详细",
    loginInfo: "登录信息",
    requestInfo: "请求信息",
    operModule: "操作模块",
    operMethod: "操作方法",
    operParam: "请求参数",
    jsonResult: "返回参数",
    normal: "正常",
    fail: "失败",
    errorMsg: "异常信息",
    millisecond: "毫秒",
    deleteConfirm: '是否确认删除日志编号为"{operIds}"的数据项?',
    cleanConfirm: "是否确认清空所有操作日志数据项?",
    cleanSuccess: "清空成功",
    operIpPlaceholder: "请输入操作地址",
    titlePlaceholder: "请输入系统模块",
    operNamePlaceholder: "请输入操作人员",
    businessTypePlaceholder: "操作类型",
    statusPlaceholder: "操作状态",
    startDate: "开始日期",
    endDate: "结束日期",
    clean: "清空",
  },
  logininfor: {
    loginId: "登录编号",
    loginName: "登录名称",
    loginNamePlaceholder: "请输入登录名称",
    status: "状态",
    statusPlaceholder: "登录状态",
    startDate: "开始日期",
    endDate: "结束日期",
    clean: "清空",
    deleteConfirm: '是否确认删除登录编号为"{loginIds}"的数据项?',
    cleanConfirm: "是否确认清空所有登录信息数据项?",
  },
  dept: {
    deptId: "部门编号",
    deptName: "部门名称",
    deptNamePlaceholder: "请输入部门名称",
    deptNameEng: "英文名称",
    deptNameEngPlaceholder: "请输入英文名称",
    status: "状态",
    email: "邮箱",
    emailPlaceholder: "请输入邮箱",
  },

  notice: {
    noticeId: "公告编号",
    noticeTitle: "公告标题",
    noticeTitlePlaceholder: "请输入公告标题",
    noticeType: "公告类型",
    noticeTypePlaceholder: "请选择公告类型",
    status: "状态",
    createBy: "创建者",
    createByPlaceholder: "请输入创建者",
    createTime: "创建时间",
    content: "内容",
  },
  post: {
    postId: "岗位编号",
    postName: "岗位名称",
    postNamePlaceholder: "请输入岗位名称",
    postCode: "岗位编码",
    postCodePlaceholder: "请输入岗位编码",
    postSort: "显示顺序",
    postSortPlaceholder: "请输入显示顺序",
    status: "状态",
    statusPlaceholder: "请选择岗位状态",
    createTime: "创建时间",
    remark: "备注",
    remarkPlaceholder: "请输入备注",
  },
  dict: {
    dictId: "字典编号",
    dictName: "字典名称",
    dictNamePlaceholder: "请输入字典名称",
    dictType: "字典类型",
    dictTypePlaceholder: "请输入字典类型",
    status: "状态",
    statusPlaceholder: "请选择字典状态",
    createTime: "创建时间",
    remark: "备注",
    remarkPlaceholder: "请输入备注",
    dictNameRequired: "字典名称不能为空",
    dictTypeRequired: "字典类型不能为空",
  },
  config: {
    configId: "参数编号",
    configName: "参数名称",
    configNamePlaceholder: "请输入参数名称",
    configKey: "参数键名",
    configKeyPlaceholder: "请输入参数键名",
    configValue: "参数键值",
    configValuePlaceholder: "请输入参数键值",
    configType: "系统内置",
    configTypePlaceholder: "请选择系统内置",
    createTime: "创建时间",
    remark: "备注",
    remarkPlaceholder: "请输入备注",
    configNameRequired: "参数名称不能为空",
    configKeyRequired: "参数键名不能为空",
    configValueRequired: "参数键值不能为空",
    refreshCache: "刷新缓存",
  },
  oss: {
    ossId: "对象存储编号",
    fileName: "文件名",
    fileNamePlaceholder: "请输入文件名",
    fileUrl: "文件URL",
    fileUrlPlaceholder: "请输入文件URL",
    createTime: "创建时间",
    createTimePlaceholder: "请选择创建时间",
    startDate: "开始日期",
    endDate: "结束日期",
    clean: "清空",
    deleteConfirm: '是否确认删除对象存储编号为"{ossIds}"的数据项?',
    cleanConfirm: "是否确认清空所有对象存储数据项?",
    fileNamePlaceholder: "请输入文件名",
    fileUrlPlaceholder: "请输入文件URL",
    download: "下载",
    originalName: "文件名",
    originalNamePlaceholder: "请输入文件名",
    fileSuffix: "文件后缀",
    fileSize: "文件大小",
    fileType: "文件类型",
    fileTypePlaceholder: "请选择文件类型",
    fileSizePlaceholder: "请输入文件大小",
    fileSuffixPlaceholder: "请输入文件后缀",
    createBy: "创建者",
    createByPlaceholder: "请输入创建者",
    service: "服务",
    servicePlaceholder: "请输入服务",
    fileTypePlaceholder: "请选择文件类型",
    fileSizePlaceholder: "请输入文件大小",
    fileSuffixPlaceholder: "请输入文件后缀",
    uploadFile: "上传文件",
    uploadImage: "上传图片",
    previewSwitch: "预览开关",
    disable: "禁用",
    enabled: "启用",
    configManagement: "配置管理",
    filePreview: "文件预览",
  },
  setting: {
    systemLogo: "系统Logo",
    systemNameZh: "系统名称(中文)",
    systemNameZhPlaceholder: "请输入系统中文名称",
    systemNameEn: "系统名称(英文)",
    systemNameEnPlaceholder: "请输入系统英文名称",
    systemNameZhRequired: "系统中文名称不能为空",
    getSystemInfoFailed: "获取系统信息失败",
    saveSystemInfoSuccess: "保存系统信息成功",
    saveSystemInfoFailed: "保存系统信息失败",
  },
  // 系统设置模块 END
  map: {
    title: "地图",
    cluster: "点聚合",
    raster: "栅格图层",
    language: "语言切换",
    chinese: "中文",
    english: "英文",
  },
  colonization: {
    title: "入侵系统",
    algorithmModel: "算法模型",
    getOccurrenceData: "获取发生数据",
    getEnvironmentData: "获取环境数据",
    modelingAnalysis: "建模分析",
    getway: "获取方式",
    getwayPlaceholder: "请选择获取方式",
    database: "数据库",
    databasePlaceholder: "请选择数据库",
    fileName: "文件名称",
    fileNamePlaceholder: "请输入文件名称",
    speciesName: "物种名称",
    speciesNamePlaceholder: "请输入物种名称",
    dataAmount: "获取数据量",
    dataAmountPlaceholder: "请输入获取数据量",
    uploadFile: "上传文件",
    uploadFileTip:
      "将xls、xlsx格式文件拖到此处，或点击上传（注意:需优化数据优化后无法直接用于算法执行如要执行算法请按照最终数据模板上传）",
    nowWeatherFiles: "现代气候数据",
    tomWeatherFiles: "未来气候数据",
    futureWeatherFiles: "未来气候数据",
    nowWeatherFilesPlaceholder: "请选择现代气候数据",
    futureWeatherFilesPlaceholder: "请选择未来气候数据",
    modelAlgorithm: "模型算法",
    modelAlgorithmPlaceholder: "请选择模型算法",

    // biomod 相关翻译
    biomodSpeciesName: "物种名称",
    biomodSpeciesNamePlaceholder:
      "请输入物种名称，请和发生数据文件里的名称保持一致",
    pseudoAbsenceRep: "伪存在点重复次数",
    pseudoAbsenceRepPlaceholder: "请输入伪存在点重复次数",
    pseudoAbsenceNum: "产生伪存在点数量",
    pseudoAbsenceNumPlaceholder: "请输入产生伪存在点数量",
    pseudoAbsenceStrategy: "伪存在点生成策略",
    pseudoAbsenceStrategyPlaceholder: "请选择伪存在点生成策略",
    modelName: "模型名称",
    modelNamePlaceholder: "请输入模型名称",
    minDistance: "最小距离",
    minDistancePlaceholder: "请输入最小距离",
    maxDistance: "最大距离",
    maxDistancePlaceholder: "请输入最大距离",
    modelValidationMethod: "模型验证方法",
    modelValidationMethodPlaceholder: "请选择模型验证方法",
    modelAlgorithms: "模型算法",
    modelAlgorithmsPlaceholder: "请选择模型算法",
    modelRepetitions: "模型重复次数",
    modelRepetitionsPlaceholder: "请输入模型重复次数",
    trainingSetRatio: "训练集比例",
    trainingSetRatioPlaceholder: "请输入训练集比例",
    kFoldValidation: "K折交叉验证",
    kFoldValidationPlaceholder: "请输入K折交叉验证",
    modelEvaluationMetrics: "模型评估指标",
    modelEvaluationMetricsPlaceholder: "请选择模型评估指标",
    ensembleMethods: "集成方法",
    ensembleMethodsPlaceholder: "请选择集成方法",
    modelSelectionMetrics: "模型筛选指标",
    modelSelectionMetricsPlaceholder: "请选择模型筛选指标",
    environmentDataMetrics: "环境数据指标",
    environmentDataMetricsPlaceholder: "请选择环境数据指标",
    selectionThreshold: "筛选阈值",
    selectionThresholdPlaceholder: "请合理填写阈值",

    // 按钮和操作
    sync: "同步",
    reset: "重置",
    optimize: "优化",
    viewOptimizationResults: "查看优化结果",
    algorithmCall: "算法调用",
    refresh: "刷新",
    downloadResults: "下载结果",
    noData: "暂无数据",
    sparsificationDistance: "稀疏化距离(km)",
    sparsificationDistancePlaceholder: "请输入稀疏化距离（单位：公里）",
    repeatRunTimes: "重复运行次数",
    repeatRunTimesPlaceholder: "请输入重复运行次数",
    correlationCoefficient: "相关系数",
    correlationCoefficientPlaceholder: "请输入相关系数",

    // 弹框标题
    occurrenceDataOptimizationParams: "发生数据优化参数",
    environmentDataOptimizationParams: "环境数据优化参数",
    environmentDataOptimizationResults: "环境数据优化结果",

    // 表格相关
    exportOccurrenceData: "导出发生数据",
    exportColonizationData: "下载Biomod2算法结果",
    speciesName: "物种名称",
    longitude: "经度",
    latitude: "纬度",
    modifiedTime: "修改时间",

    // 提示信息
    dataInfo: "数据信息",
    resultPreview: "结果预览",

    // 回归和分类算法相关翻译
    isFinetune: "是否微调",
    trainingRatio: "训练集比例",
    trainingRatioPlaceholder: "请输入训练集比例0~1",
    pretrainFile: "预训练文件",
    predictFile: "预测文件",
    finetuneFile: "微调文件",

    // biomod相关翻译
    modelName: "模型名称",
    modelNamePlaceholder: "请输入模型名称",
    minDistance: "最小距离",
    minDistancePlaceholder: "请输入最小距离",
    maxDistance: "最大距离",
    maxDistancePlaceholder: "请输入最大距离",
    modelValidationMethod: "模型验证方法",
    modelValidationMethodPlaceholder: "请选择模型验证方法",
    modelAlgorithms: "模型算法",
    modelAlgorithmsPlaceholder: "请选择模型算法",
    modelRepetitions: "模型重复次数",
    modelRepetitionsPlaceholder: "请输入模型重复次数",
    trainingSetRatio: "训练集比例",
    trainingSetRatioPlaceholder: "请输入训练集比例",
    kFoldValidation: "K折交叉验证",
    kFoldValidationPlaceholder: "请输入K折交叉验证",
    modelEvaluationMetrics: "模型评估指标",
    modelEvaluationMetricsPlaceholder: "请选择模型评估指标",
    ensembleMethods: "集成方法",
    ensembleMethodsPlaceholder: "请选择集成方法",
    modelSelectionMetrics: "模型筛选指标",
    modelSelectionMetricsPlaceholder: "请选择模型筛选指标",
    environmentDataMetrics: "环境数据指标",
    environmentDataMetricsPlaceholder: "请选择环境数据指标",
    selectionThresholdPlaceholder: "请合理填写阈值",
    pseudoAbsenceStrategyPlaceholder: "请选择伪存在点生成策略",

    // 表单验证消息
    validation: {
      selectGetway: "请选择获取方式",
      selectDatabase: "请选择数据库",
      enterSpeciesName: "请输入物种名称",
      enterDataAmount: "请输入获取数据量",
      uploadOccurrenceFile: "请上传发生数据文件",
      selectModernClimate: "请选择或上传现代气候数据",
      selectFutureClimate: "请选择或上传未来气候数据",
      selectFutureWeather: "请选择未来气候数据",
      selectAlgorithm: "请选择模型算法",
      enterTrainingRatio: "请输入训练集比例",
      uploadPretrainFile: "请上传预训练文件",
      uploadPredictFile: "请上传预测文件",
      uploadFinetuneFile: "请上传微调文件",
      enterSpeciesNameBiomod: "请输入物种名称",
      enterPseudoAbsenceRep: "请输入伪存在点重复次数",
      enterPseudoAbsenceNum: "请输入产生伪存在点数量",
      selectPseudoAbsenceStrategy: "请选择伪存在点生成策略",
      enterModelName: "请输入模型名称",
      enterMinDistance: "请输入最小距离",
      enterMaxDistance: "请输入最大距离",
      selectModelValidation: "请选择模型验证方法",
      selectModelAlgorithms: "请选择模型算法",
      enterModelRepetitions: "请输入模型重复次数",
      enterTrainingSetRatio: "请输入训练集比例",
      enterKFoldValidation: "请输入K折交叉验证",
      selectModelEvaluation: "请选择模型评估指标",
      selectEnsembleMethods: "请选择集成方法",
      selectModelSelection: "请选择模型筛选指标",
      selectEnvironmentData: "请选择环境数据指标",
      enterSelectionThreshold: "请输入筛选阈值",
      enterThinPar: "请输入稀疏化距离参数",
      enterReps: "请输入重复运行次数参数",
      enterCorrelationCoefficient: "请输入相关系数",
    },
  },

  // 文件上传相关翻译
  upload: {
    selectFile: "选取文件",
    pleaseUpload: "请上传",
    sizeLimit: "大小不超过",
    formatLimit: "格式为",
    fileText: "的文件",
    downloadTemplate: "下载模板",
    delete: "删除",
    formatError: "文件格式不正确, 请上传{formats}格式文件!",
    sizeError: "上传文件大小不能超过 {size} MB!",
    limitError: "上传文件数量不能超过 {limit} 个!",
    uploadFailed: "上传文件失败",
    uploading: "正在上传文件，请稍候...",
  },

  // 扩散分析相关翻译
  spread: {
    modelAlgorithm: "模型算法",
    basicSettings: "基础设置",
    modelParameters: "模型参数",
    randomSeed: "随机数种子",
    randomSeedPlaceholder: "请输入随机数种子",
    inputDataType: "输入数据类型",
    inputDataTypePlaceholder: "请选择输入数据类型",
    longitude: "经度",
    longitudePlaceholder: "请输入经度",
    latitude: "纬度",
    latitudePlaceholder: "请输入纬度",
    initialInvasionProbability: "初始入侵概率",
    initialInvasionProbabilityPlaceholder: "请输入初始入侵概率",
    simulationRepetitions: "模拟重复次数",
    simulationRepetitionsPlaceholder: "请输入模拟重复次数",
    simulationIterations: "模拟迭代次数(年)",
    simulationIterationsPlaceholder: "请输入模拟迭代次数",
    optimizationFunction: "优化函数",
    optimizationFunctionPlaceholder: "请选择优化函数",
    optimize: "优化",
    resultPreview: "结果预览",
    dataInfo: "数据信息",
    parameterOptimization: "参数优化",
    firstInvasionYear: "首次入侵年份",
    downloadExecutionResults: "下载执行结果",
    execute: "执行",
    jumpDiffusionFrequency: "跳跃式扩散频率",
    jumpDiffusionFrequencyPlaceholder: "请输入跳跃式扩散频率",
    jumpDiffusionDistanceDistribution: "跳跃式扩散距离分布",
    jumpDiffusionDistanceDistributionPlaceholder: "请选择跳跃式扩散距离分布",
    jumpDiffusionDistanceMean: "跳跃式扩散距离分布均值",
    jumpDiffusionDistanceMeanPlaceholder: "请输入跳跃式扩散距离分布均值",
    jumpDiffusionGammaParameter: "跳跃式扩散分布伽马参数",
    jumpDiffusionGammaParameterPlaceholder: "请输入跳跃式扩散分布伽马参数",
    humanActivityIndexWeighting: "是否适用人类活动指数加权",
    humanActivityIndexWeightingPlaceholder: "请选择是否适用人类活动指数加权",
    jumpDiffusionDistanceVariance: "跳跃式扩散距离分布方差",
    jumpDiffusionDistanceVariancePlaceholder: "请输入跳跃式扩散距离分布方差",
    roadWeight: "道路权重",
    roadWeightClass1: "Ⅰ类道路权重",
    roadWeightClass1Placeholder: "请输入Ⅰ类道路权重",
    roadWeightClass2: "Ⅱ类道路权重",
    roadWeightClass2Placeholder: "请输入Ⅱ类道路权重",
    roadWeightClass3: "Ⅲ类道路权重",
    roadWeightClass3Placeholder: "请输入Ⅲ类道路权重",
    roadWeightClass4: "Ⅳ类道路权重",
    roadWeightClass4Placeholder: "请输入Ⅳ类道路权重",
    roadWeightClass5: "Ⅴ类道路权重",
    roadWeightClass5Placeholder: "请输入Ⅴ类道路权重",
    roadWeightClass6: "Ⅵ类道路权重",
    roadWeightClass6Placeholder: "请输入Ⅵ类道路权重",
    minimumWeightBetweenCells: "单元格之间最小权重",
    minimumWeightBetweenCellsPlaceholder: "请输入单元格之间最小权重",
    inputFiles: "输入文件",
    roadNetworkFile: "道路网络文件",
    mapFile: "地图文件",
    observationDataFile: "观测数据文件",
    parameterDataFile: "参数数据文件",
  },

  // 风险分析相关翻译
  risk: {
    riskNodeCentralityAnalysis: "风险节点中心性分析",
    clusteringResults: "聚类结果",
    analysisResults: "分析结果",
    dragFileHere: "将xls、xlsx格式文件拖到此处，或",
    clickToUpload: "点击上传",
    runDirectedNetwork: "运行有向网络",
    runUndirectedNetwork: "运行无向网络",
    clusteringMethod: "聚类方法",
    clusteringMethodPlaceholder: "请选择聚类方法",
    riskAssessmentIndicators: "风险评估指标",
    riskAssessmentIndicatorsPlaceholder: "请选择风险评估指标，至少选择两项",
    removalQuantityPerTime: "每次移除数量",
    removalQuantityPerTimePlaceholder: "请输入每次移除数量",
    selectInterfaceLanguage: "选择界面语言",
    analysisRun: "分析运行",
    downloadReport: "下载报告",
    startClustering: "开始聚类",
    startAnalysis: "开始分析",
    exportResults: "导出结果",
    clickToScale: "点击{action}",
    restore: "还原",
    enlarge: "放大",
    noAnalysisData: "暂无分析数据",
    resultPreview: "结果预览",
    openPreviewInNewPage: "新页面打开预览",
    chinese: "中文",
    english: "英文",
  },

  // 导航栏相关翻译
  navbar: {
    welcome: "欢迎您",
    personalCenter: "个人中心",
    layoutSettings: "布局设置",
    logout: "退出登录",
    chinese: "中文",
    english: "English",
    logoutConfirm: "确定注销并退出系统吗？",
    tip: "提示",
  },

  // 个人资料相关翻译
  profile: {
    personalInfo: "个人信息",
    username: "用户名称",
    phoneNumber: "手机号码",
    email: "用户邮箱",
    department: "所属部门",
    role: "所属角色",
    createDate: "创建日期",
    basicInfo: "基本资料",
    changePassword: "修改密码",
    nickname: "用户昵称",
    gender: "性别",
    male: "男",
    female: "女",
    nicknameRequired: "用户昵称不能为空",
    emailRequired: "邮箱地址不能为空",
    emailFormat: "请输入正确的邮箱地址",
    phoneRequired: "手机号码不能为空",
    phoneFormat: "请输入正确的手机号码",
    updateSuccess: "修改成功",
    oldPassword: "旧密码",
    newPassword: "新密码",
    confirmPassword: "确认密码",
    oldPasswordPlaceholder: "请输入旧密码",
    newPasswordPlaceholder: "请输入新密码",
    confirmPasswordPlaceholder: "请确认新密码",
    oldPasswordRequired: "旧密码不能为空",
    newPasswordRequired: "新密码不能为空",
    confirmPasswordRequired: "确认密码不能为空",
    passwordLength: "长度在 6 到 20 个字符",
    passwordMismatch: "两次输入的密码不一致",

    option1: "选项一",
    option1Placeholder: "请输入选项一",
    option2: "选项二",
    option2Placeholder: "请输入选项二",
    option3: "选项三",
    option3Placeholder: "请输入选项三",
    option4: "选项四",
    option4Placeholder: "请输入选项四",
    option5: "选项五",
    option5Placeholder: "请输入选项五",
    option6: "选项六",
    option6Placeholder: "请输入选项六",
    sync: "同步",
    algorithmCall: "算法调用",
    parameterOptimization: "参数优化",
    optimizationOptions: "优化选项",
    taskStatus: "任务状态",
    taskList: "任务列表",
    map: "地图",
    dataTable: "数据表格",
  },
  login: {
    title: "入侵系统管理平台",
    username: "用户名",
    usernamePlaceholder: "请输入用户名",
    password: "密码",
    passwordPlaceholder: "请输入密码",
    captcha: "验证码",
    captchaPlaceholder: "请输入验证码",
    rememberMe: "记住我",
    login: "登录",
    register: "注册",
    forgetPassword: "忘记密码",
    loginSuccess: "登录成功",
    loginFailed: "登录失败",
    usernameRequired: "用户名不能为空",
    passwordRequired: "密码不能为空",
    captchaRequired: "验证码不能为空",
  },
  home: {
    uploadFile: "上传文件",
    uploadFileTip: "将xls、xlsx格式文件拖到此处，或点击上传",
    runDirectedNetwork: "运行有向网络",
    runUndirectedNetwork: "运行无向网络",
    clusteringMethod: "聚类方法",
    clusteringMethodPlaceholder: "请选择聚类方法",
    run: "运行",
    reset: "重置",
    results: "结果",
    dataTable: "数据表格",
  },
};

<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item :label="t('spread.firstInvasionYear')" prop="year">
        <div class="slider-demo-block v-m-l-10" style="width: 350px">
          <el-slider v-model="queryParams.year" :min="1" :max="50" />
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="search" @click="handleQuery">{{
          t("common.search")
        }}</el-button>

        <el-button
          :loading="refreshLoading"
          icon="Refresh"
          @click="handleRefresh"
          >{{ t("common.refresh") }}</el-button
        >
        <el-button
          :disabled="!resultDownloadUrl"
          type="success"
          icon="download"
          @click="importTemplate"
          >{{ t("spread.downloadExecutionResults") }}</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      border
      :data="dataList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column
        :label="item.label"
        align="center"
        :prop="item.prop"
        v-for="(item, index) in columns"
        :key="index"
        :fixed="item.fixed"
      >
        <template #default="scope" v-if="item.scope">
          <span>{{ parseTime(scope.row.modified, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty :image-size="200" description="暂无数据" />
      </template>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
    />

    <!-- 添加或修改测试单表对话框 -->
    <el-dialog :title="title" v-model="open" width="608px" append-to-body>
      <el-form
        :inline="true"
        ref="demoRef"
        :model="form"
        :rules="rules"
        label-width="70px"
      >
        <el-form-item label="物种名称" prop="scientificName">
          <el-input
            v-model="form.scientificName"
            placeholder="请输入物种名称"
          />
        </el-form-item>
        <el-form-item label="经度" prop="decimalLongitude">
          <el-input v-model="form.decimalLongitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="维度" prop="decimalLatitude">
          <el-input v-model="form.decimalLatitude" placeholder="请输入维度" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Species1">
import {
  generationDataResultList,
  generationDataUpdate,
  generationDataDelete,
} from "@/api/colonization";
import { queryUserTaskList } from "@/api/spread";
import { getToken } from "@/utils/auth";
import { t } from "@/utils/i18n";
const emit = defineEmits();
const props = defineProps({
  dataId: {
    type: String,
    default: "",
  },
  runRusultData: {
    type: Object,
    default: () => {},
  },
});

const { proxy } = getCurrentInstance();

const dataList = ref([]);
const open = ref(false);
const buttonLoading = ref(false);
const loading = ref(false);
const refreshLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);
const allDataList = ref([]); // 保存所有数据
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/organism/speciesGis/importData",
});

// 列显隐信息
const columns = ref([
  { prop: "id", label: `ID`, visible: true, fixed: true },
  { prop: "x", label: `x`, visible: true },
  { prop: "y", label: `y`, visible: true },
  { prop: "totalNbIntros", label: `total_nb_intros`, visible: true },
  { prop: "meanNbIntros", label: `mean_nb_intros`, visible: true },
  { prop: "varNbIntros", label: `var_nb_intros`, visible: true },
  { prop: "meanLastInvasion", label: `mean_last_invasion`, visible: true },
  { prop: "varLastInvasion", label: `var_last_invasion`, visible: true },
  { prop: "emptyL", label: `empty_L`, visible: true },
  { prop: "maxL", label: `max_L`, visible: true },
  { prop: "emptyScore", label: `empty_score`, visible: true },
  { prop: "yobs", label: `y_obs`, visible: true },
  { prop: "nobs", label: `n_obs`, visible: true },
  { prop: "pobs", label: `p_obs`, visible: true },
  { prop: "ysim", label: `y_sim`, visible: true },
  { prop: "nsim", label: `n_sim`, visible: true },
  { prop: "psim", label: `p_sim`, visible: true },
  { prop: "l", label: `L`, visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    year: 1,
  },
  rules: {
    testKey: [{ required: true, message: "key键不能为空", trigger: "blur" }],
    value: [{ required: true, message: "值不能为空", trigger: "blur" }],
  },
  resultDownloadUrl: "",
});

const { queryParams, form, rules, resultDownloadUrl } = toRefs(data);

// 分页变化时重新切片
watch(
  () => [queryParams.value.pageNum, queryParams.value.pageSize],
  () => {
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    dataList.value = allDataList.value.slice(start, end);
  }
);

function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
  };

  if (props.runRusultData?.data?.length > 0) {
    allDataList.value = props.runRusultData?.data;
    total.value = allDataList?.value?.length;
    resultDownloadUrl.value = props.runRusultData?.file;
    // 计算当前页数据
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    dataList.value = allDataList.value.slice(start, end);
    loading.value = false;
    emit("getSourceList", { dataList: allDataList.value });
    return;
  }

  queryUserTaskList(params)
    .then((response) => {
      // 假分页：一次性拉全量数据
      allDataList.value = response?.data?.data || [];
      total.value = allDataList?.value?.length;
      resultDownloadUrl.value = response?.data?.file;
      // 计算当前页数据
      const start =
        (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
      const end = start + queryParams.value.pageSize;
      dataList.value = allDataList.value.slice(start, end);
      loading.value = false;
      emit("getSourceList", {
        dataList: response?.data?.data,
        pSimRange: {
          min: response?.data?.min,
          max: response?.data?.max,
        },
      });
    })
    .catch(() => {
      loading.value = false;
    })
    .finally(() => {
      emit("updateTaskStatus");
      loading.value = false;
      refreshLoading.value = false;
    });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    scientificName: undefined,
    decimalLongitude: undefined,
    decimalLatitude: undefined,
    createTime: undefined,
    createBy: undefined,
    updateTime: undefined,
    updateBy: undefined,
  };
  proxy.resetForm("demoRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 修改按钮操作 */
function handleRefresh(row) {
  refreshLoading.value = true;
  getList();
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["demoRef"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      generationDataUpdate(form.value)
        .then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
        });
    }
  });
}

function importTemplate() {
  if (!resultDownloadUrl.value) return;
  window.open(
    `${window.location.protocol}//${window.location.host}/file/${resultDownloadUrl.value}`,
    "_blank"
  );
}

watch(
  () => props.dataId,
  () => {
    getList();
  }
);
</script>

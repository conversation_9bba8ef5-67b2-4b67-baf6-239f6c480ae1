<template>
  <div class="page-container v-p-20">
    <el-row :gutter="20">
      <el-col :span="10">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("spread.modelAlgorithm") }}</div>
            </div>
          </template>
          <div class="v-m-l-8">
            <el-form
              size="large"
              class="v-p-y-10"
              :model="queryParams.dataForm"
              ref="queryRef"
              label-width="110px"
              :inline="false"
              :rules="queryParams.fomRules"
            >
              <!-- 基础设置 -->
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Document /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("spread.basicSettings") }}
                  </div>
                </div>
                <el-icon
                  @click="layoutSetting.showBasic = !layoutSetting.showBasic"
                  class="v-pointer"
                  v-show="layoutSetting.showBasic"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="layoutSetting.showBasic = !layoutSetting.showBasic"
                  class="v-pointer"
                  v-show="!layoutSetting.showBasic"
                  ><ArrowUp
                /></el-icon>
              </div>

              <div v-show="layoutSetting.showBasic">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.randomSeed')"
                      class="v-m-y-10"
                      prop="seed"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.seed"
                        :min="1"
                        :max="100000"
                        :precision="0"
                        style="width: 100%"
                        :placeholder="t('spread.randomSeedPlaceholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      class="v-m-y-10"
                      :label="t('spread.inputDataType')"
                      prop="typeofdata"
                    >
                      <el-select
                        v-model="queryParams.dataForm.typeofdata"
                        :placeholder="t('spread.inputDataTypePlaceholder')"
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in spread_analysis_typeofdata"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <!-- 模型参数 -->
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20 v-m-t-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Menu /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("spread.modelParameters") }}
                  </div>
                </div>
                <el-icon
                  @click="
                    layoutSetting.showModelParam = !layoutSetting.showModelParam
                  "
                  class="v-pointer"
                  v-show="layoutSetting.showModelParam"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="
                    layoutSetting.showModelParam = !layoutSetting.showModelParam
                  "
                  class="v-pointer"
                  v-show="!layoutSetting.showModelParam"
                  ><ArrowUp
                /></el-icon>
              </div>

              <div v-show="layoutSetting.showModelParam">
                <!-- <div class="sub-title v-m-t-20">模型参数</div> -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.longitude')"
                      class="v-m-y-10"
                      prop="longitude"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.longitude"
                        :min="20000"
                        :max="88000"
                        style="width: 100%"
                        :placeholder="t('spread.longitudePlaceholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.latitude')"
                      class="v-m-y-10"
                      prop="latitude"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.latitude"
                        :min="14000"
                        :max="104000"
                        style="width: 100%"
                        :placeholder="t('spread.latitudePlaceholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.initialInvasionProbability')"
                      class="v-m-y-20"
                      prop="pintro"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.pintro"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="
                          t('spread.initialInvasionProbabilityPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.simulationRepetitions')"
                      class="v-m-y-10"
                      prop="reps"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.reps"
                        :min="0"
                        :max="1000000"
                        style="width: 100%"
                        :placeholder="
                          t('spread.simulationRepetitionsPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.simulationIterations')"
                      class="v-m-t-10 v-m-b-20 v-line-24"
                      prop="iters"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.iters"
                        :min="0"
                        :max="1000000"
                        style="width: 100%"
                        :placeholder="
                          t('spread.simulationIterationsPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      class="v-m-t-10 v-m-b-20"
                      :label="t('spread.optimizationFunction')"
                      prop="optimfunc"
                    >
                      <el-select
                        v-model="queryParams.dataForm.optimfunc"
                        :placeholder="
                          t('spread.optimizationFunctionPlaceholder')
                        "
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in spread_analysis_function"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.jumpDiffusionFrequency')"
                      class="v-m-t-10 v-line-24"
                      prop="lambda"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.lambda"
                        :min="0"
                        :max="30"
                        style="width: 100%"
                        :placeholder="
                          t('spread.jumpDiffusionFrequencyPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      class="v-m-t-10 v-line-24"
                      :label="t('spread.jumpDiffusionDistanceDistribution')"
                      prop="law"
                    >
                      <el-select
                        v-model="queryParams.dataForm.law"
                        :placeholder="
                          t(
                            'spread.jumpDiffusionDistanceDistributionPlaceholder'
                          )
                        "
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in spread_analysis_distance"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row
                  :gutter="20"
                  v-if="['DIRAC', 'CAUCHY'].includes(queryParams.dataForm.law)"
                >
                  <el-col :span="12">
                    <el-form-item
                      class="v-m-t-10 v-line-24"
                      v-if="queryParams.dataForm.law == 'DIRAC'"
                      :label="t('spread.jumpDiffusionDistanceMean')"
                      prop="mu"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.mu"
                        :min="0"
                        :max="100000"
                        style="width: 100%"
                        :placeholder="
                          t('spread.jumpDiffusionDistanceMeanPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="t('spread.jumpDiffusionGammaParameter')"
                      v-if="queryParams.dataForm.law == 'CAUCHY'"
                      class="v-m-t-10 v-line-24"
                      prop="gamma"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.gamma"
                        :min="0"
                        :max="10"
                        style="width: 100%"
                        :placeholder="
                          t('spread.jumpDiffusionGammaParameterPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.humanActivityIndexWeighting')"
                      prop="humanactivity"
                      class="v-m-t-10 v-line-24"
                    >
                      <el-select
                        v-model="queryParams.dataForm.humanactivity"
                        :placeholder="
                          t('spread.humanActivityIndexWeightingPlaceholder')
                        "
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in spread_yes_no"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row
                  :gutter="20"
                  v-if="
                    ['NORMAL', 'LOG_NORMAL'].includes(queryParams.dataForm.law)
                  "
                >
                  <el-col :span="12">
                    <el-form-item
                      class="v-m-t-10 v-line-24"
                      v-if="queryParams.dataForm.law != 'CAUCHY'"
                      :label="t('spread.jumpDiffusionDistanceMean')"
                      prop="mu"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.mu"
                        :min="0"
                        :max="100000"
                        style="width: 100%"
                        :placeholder="
                          t('spread.jumpDiffusionDistanceMeanPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.jumpDiffusionDistanceVariance')"
                      v-if="
                        ['NORMAL', 'LOG_NORMAL'].includes(
                          queryParams.dataForm.law
                        )
                      "
                      class="v-m-t-10 v-line-24"
                      prop="sigma"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.sigma"
                        :min="0.1"
                        :max="3"
                        style="width: 100%"
                        :placeholder="
                          t('spread.jumpDiffusionDistanceVariancePlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  :gutter="20"
                  v-if="
                    ['NORMAL', 'LOG_NORMAL'].includes(queryParams.dataForm.law)
                  "
                >
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.humanActivityIndexWeighting')"
                      prop="humanactivity"
                      class="v-m-t-10 v-line-24"
                    >
                      <el-select
                        v-model="queryParams.dataForm.humanactivity"
                        :placeholder="
                          t('spread.humanActivityIndexWeightingPlaceholder')
                        "
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in spread_yes_no"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <!-- 道路权重 -->
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20 v-m-t-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Odometer /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("spread.roadWeight") }}
                  </div>
                </div>

                <el-icon
                  @click="
                    layoutSetting.showRoadWeight = !layoutSetting.showRoadWeight
                  "
                  class="v-pointer"
                  v-show="layoutSetting.showRoadWeight"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="
                    layoutSetting.showRoadWeight = !layoutSetting.showRoadWeight
                  "
                  class="v-pointer"
                  v-show="!layoutSetting.showRoadWeight"
                  ><ArrowUp
                /></el-icon>
              </div>

              <!-- <div class="sub-title v-m-t-80">道路权重</div> -->
              <div v-show="layoutSetting.showRoadWeight">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass1')"
                      class="v-m-y-10"
                      prop="w1"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w1"
                        :min="0"
                        :max="100000"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass1Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass2')"
                      class="v-m-y-10"
                      prop="w2"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w2"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass2Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass3')"
                      class="v-m-y-10"
                      prop="w3"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w3"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass3Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass4')"
                      class="v-m-y-10"
                      prop="w4"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w4"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass4Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass5')"
                      class="v-m-y-10"
                      prop="w5"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w5"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass5Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label-width="110px"
                      :label="t('spread.roadWeightClass6')"
                      class="v-m-y-10"
                      prop="w6"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.w6"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="t('spread.roadWeightClass6Placeholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="t('spread.minimumWeightBetweenCells')"
                      class="v-m-y-10 v-line-24"
                      label-width="110px"
                      prop="wmin"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.wmin"
                        :min="0"
                        :max="1"
                        style="width: 100%"
                        :placeholder="
                          t('spread.minimumWeightBetweenCellsPlaceholder')
                        "
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <!-- 输入文件 -->
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20 v-m-t-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Folder /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("spread.inputFiles") }}
                  </div>
                </div>

                <el-icon
                  @click="
                    layoutSetting.showInputFile = !layoutSetting.showInputFile
                  "
                  class="v-pointer"
                  v-show="layoutSetting.showInputFile"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="
                    layoutSetting.showInputFile = !layoutSetting.showInputFile
                  "
                  class="v-pointer"
                  v-show="!layoutSetting.showInputFile"
                  ><ArrowUp
                /></el-icon>
              </div>

              <!-- <div class="sub-title v-m-t-40">输入文件</div> -->
              <div v-show="layoutSetting.showInputFile">
                <el-form-item
                  :label="t('spread.roadNetworkFile')"
                  class="v-m-y-10"
                  label-width="110px"
                  prop="network"
                >
                  <uploadEnvFile
                    :fileType="['txt']"
                    v-model="queryParams.dataForm.network"
                    type="futureUploadFile"
                    style="min-width: 300px"
                  />
                </el-form-item>
                <el-form-item
                  label-width="110px"
                  :label="t('spread.mapFile')"
                  class="v-m-y-10"
                  prop="map"
                >
                  <uploadEnvFile
                    :fileType="['txt']"
                    v-model="queryParams.dataForm.map"
                    type="futureUploadFile"
                    style="min-width: 300px"
                  />
                </el-form-item>
                <el-form-item
                  :label="t('spread.observationDataFile')"
                  class="v-m-y-10"
                  label-width="110px"
                  prop="sample"
                >
                  <uploadEnvFile
                    :fileType="['txt']"
                    v-model="queryParams.dataForm.sample"
                    type="futureUploadFile"
                    style="min-width: 300px"
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>

          <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
            <el-tooltip
              class="box-item"
              effect="light"
              :disabled="!hasTaskList"
              :content="
                hasTaskList
                  ? '任务正在执行中,请点击右侧列表刷新查看任务状态'
                  : ''
              "
              placement="top-start"
              ><el-button
                :disabled="hasTaskList"
                type="success"
                @click="handleRun"
                :loading="queryParams.btnLoading"
                icon="refresh"
                size="large"
                >{{ t("spread.execute") }}</el-button
              ></el-tooltip
            >

            <el-button
              size="large"
              icon="DocumentDelete"
              @click="handleReset"
              >{{ t("common.reset") }}</el-button
            >
            <el-tooltip
              class="box-item"
              effect="light"
              :disabled="!hasTaskList"
              :content="
                hasTaskList
                  ? '任务正在执行中,请点击右侧列表刷新查看任务状态'
                  : ''
              "
              placement="top-start"
            >
              <el-button
                :disabled="hasTaskList"
                type="primary"
                icon="refresh"
                size="large"
                @click="handleRefresh"
                >{{ t("spread.optimize") }}</el-button
              >
            </el-tooltip>
          </div>
        </el-card>
      </el-col>

      <el-col :span="14">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("spread.resultPreview") }}</div>
            </div>
          </template>
          <spreadMap
            :pSimRange="pSimRange"
            :key="updateMapKey"
            :lang="currentLanguage"
            :sourceData="sourceList"
          />
        </el-card>
        <el-card shadow="always" class="v-m-y-20">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("spread.dataInfo") }}</div>
            </div>
          </template>
          <tableList
            :key="updateTableKey"
            :runRusultData="runRusultData"
            @getSourceList="getSourceList"
            @updateTaskStatus="updateTaskStatus"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 参数优化表对话框 -->
    <el-dialog
      :title="preferParams.title"
      v-model="preferParams.open"
      v-if="preferParams.open"
      width="608px"
      append-to-body
    >
      <div>
        <el-form
          :inline="true"
          ref="preferParamsRef"
          :model="preferParams.dataForm"
          :rules="preferParams.rules"
          label-width="120px"
          :key="updateDialogKey"
        >
          <el-form-item
            :label="t('spread.roadNetworkFile')"
            class="v-m-y-10"
            prop="networkFileId"
          >
            <uploadEnvFile
              :fileType="['txt']"
              v-model="preferParams.dataForm.networkFileId"
              type="futureUploadFile"
              style="min-width: 300px"
            />
          </el-form-item>
          <el-form-item
            :label="t('spread.mapFile')"
            class="v-m-y-10"
            prop="mapFileId"
          >
            <uploadEnvFile
              :fileType="['txt']"
              v-model="preferParams.dataForm.mapFileId"
              type="futureUploadFile"
              style="min-width: 300px"
            />
          </el-form-item>
          <el-form-item
            :label="t('spread.observationDataFile')"
            class="v-m-y-10"
            prop="sampleFileId"
          >
            <uploadEnvFile
              :fileType="['txt']"
              v-model="preferParams.dataForm.sampleFileId"
              type="futureUploadFile"
              style="min-width: 300px"
            />
          </el-form-item>

          <el-form-item
            :label="t('spread.parameterDataFile')"
            class="v-m-y-10"
            prop="parametersFileId"
          >
            <uploadEnvFile
              :fileType="['txt']"
              v-model="preferParams.dataForm.parametersFileId"
              type="futureUploadFile"
              style="min-width: 300px"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :loading="preferParams.submitLoading"
            type="primary"
            @click="submitForm"
            >{{ t("common.confirm") }}</el-button
          >
          <el-button @click="preferParams.open = false">{{
            t("common.cancel")
          }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Spread">
import { getToken } from "@/utils/auth";
import spreadMap from "@/components/Map/AdvancedMap.vue";
import uploadEnvFile from "./components/uploadEnvFile.vue";
import tableList from "./components/tableList.vue";
import { t } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from "@/composables/useLanguageChange";
import {
  ref,
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
  computed,
  watch,
  nextTick,
} from "vue";
import {
  morisOptimizeResult,
  queryTaskStatus,
  queryRunTask,
} from "@/api/spread";
const { proxy } = getCurrentInstance();

// 语言相关
const languageStore = useLanguageStore();
const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

const {
  spread_analysis_typeofdata,
  spread_analysis_function,
  spread_analysis_distance,
  spread_yes_no,
} = proxy.useDict(
  "spread_analysis_typeofdata",
  "spread_analysis_function",
  "spread_analysis_distance",
  "spread_yes_no"
);

// 计算属性：根据语言返回字典项的显示文本
const getDictLabel = (dict) => {
  return currentLanguage.value === "en" ? dict.value : dict.label;
};

const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
});
const data = reactive({
  preferParams: {
    // 是否显示弹出层
    open: false,
    // 弹出层标题（用户导入）
    title: computed(() => t("spread.parameterOptimization")),
    // 是否禁用上传
    isUploading: false,

    dataForm: {
      networkFileId: undefined,
      mapFileId: undefined,
      sampleFileId: undefined,
      parametersFileId: undefined,
    },
    submitLoading: false,
    rules: {
      networkFileId: [
        {
          required: true,
          message: "请选择道路网络文件",
          trigger: "change",
        },
      ],
      mapFileId: [
        {
          required: true,
          message: "请选择地图文件",
          trigger: "change",
        },
      ],
      sampleFileId: [
        {
          required: true,
          message: "请选择地图文件",
          trigger: "change",
        },
      ],
      parametersFileId: [
        {
          required: true,
          message: "请选择参数数据文件",
          trigger: "change",
        },
      ],
    },
    updateDialogKey: 0,
  },
  queryParams: {
    dataForm: {},
    fomRules: {
      seed: [{ required: true, message: "请输入随机数种子", trigger: "blur" }],
      typeofdata: [
        { required: true, message: "请选择输入数据类型", trigger: "change" },
      ],
      longitude: [{ required: true, message: "请输入经度", trigger: "blur" }],
      latitude: [{ required: true, message: "请输入纬度", trigger: "blur" }],
      pintro: [
        { required: true, message: "请输入初始入侵概率", trigger: "blur" },
      ],
      reps: [
        { required: true, message: "请输入模拟重复次数", trigger: "blur" },
      ],
      iters: [
        { required: true, message: "请输入模拟迭代次数", trigger: "blur" },
      ],
      optimfunc: [
        { required: true, message: "请选择优化函数", trigger: "change" },
      ],
      lambda: [
        { required: true, message: "请输入跳跃式扩散频率", trigger: "blur" },
      ],
      law: [
        {
          required: true,
          message: "请选择跳跃式扩散距离分布",
          trigger: "change",
        },
      ],
      mu: [
        {
          required: true,
          message: "请输入跳跃式扩散距离分布均值",
          trigger: "blur",
        },
      ],
      sigma: [
        {
          required: true,
          message: "请输入跳跃式扩散距离分布方差",
          trigger: "blur",
        },
      ],
      gamma: [
        {
          required: true,
          message: "请输入跳跃式扩散分布伽马参数",
          trigger: "blur",
        },
      ],
      humanactivity: [
        {
          required: true,
          message: "请选择是否适用人类活动指数加权",
          trigger: "change",
        },
      ],
      w1: [{ required: true, message: "请输入Ⅰ类道路权重", trigger: "blur" }],
      w2: [{ required: true, message: "请输入Ⅱ类道路权重", trigger: "blur" }],
      w3: [{ required: true, message: "请输入Ⅲ类道路权重", trigger: "blur" }],
      w4: [{ required: true, message: "请输入Ⅳ类道路权重", trigger: "blur" }],
      w5: [{ required: true, message: "请输入Ⅴ类道路权重", trigger: "blur" }],
      w6: [{ required: true, message: "请输入Ⅵ类道路权重", trigger: "blur" }],
      wmin: [
        {
          required: true,
          message: "请输入单元格之间最小权重",
          trigger: "blur",
        },
      ],
      network: [
        { required: true, message: "请选择道路网络文件", trigger: "change" },
      ],
      map: [{ required: true, message: "请选择地图文件", trigger: "change" }],
      sample: [
        { required: true, message: "请选择观测数据文件", trigger: "change" },
      ],
    },
    btnLoading: false,
  },

  // 布局设置 展开折叠
  layoutSetting: {
    showBasic: true,
    showModelParam: true,
    showRoadWeight: true,
    showInputFile: true,
  },
  hasTaskList: false, // 是否有正在执行的任务
  sourceList: [],
  updateMapKey: 0, // 更新地图
  runRusultData: {},
  updateTableKey: 0, // 更新表格
  pSimRange: {
    min: 0,
    max: 1,
  },
});

const {
  queryParams,
  layoutSetting,
  preferParams,
  hasTaskList,
  sourceList,
  pSimRange,
} = toRefs(data);

// 重置
const handleReset = () => {
  queryParams.value.dataForm = {
    seed: undefined,
    typeofdata: "PRESENCE_ABSENCE",
    longitude: 30000,
    latitude: 30000,
    pintro: 1,
    reps: 1000,
    iters: 25,
    optimfunc: "LOG_LIKELIHOOD",
    lambda: 1,
    law: "LOG_NORMAL",
    mu: 0,
    sigma: 0,
    gamma: undefined,
    humanactivity: "YES",
    w1: 1,
    w2: 1,
    w3: 1,
    w4: 1,
    w5: 0,
    w6: 0,
    wmin: 0,
    network: undefined,
    map: undefined,
    sample: undefined,
  };
};
// 参数优化
function handleRefresh() {
  preferParams.value.dataForm = {
    networkFileId: undefined,
    mapFileId: undefined,
    sampleFileId: undefined,
    parametersFileId: undefined,
  };
  preferParams.value.updateDialogKey++;
  preferParams.value.open = true;
  preferParams.value.title = "优化选项";
}
// 优化提交逻辑
function submitForm() {
  proxy.$refs["preferParamsRef"].validate((valid) => {
    if (valid) {
      preferParams.value.submitLoading = true;
      morisOptimizeResult(preferParams.value.dataForm)
        .then((response) => {
          proxy.$modal.msgSuccess(
            "优化算法已执行，请稍后在数据信息点击刷新查看"
          );
          preferParams.value.open = false;
          getTaskStatus();
        })
        .finally(() => {
          preferParams.value.submitLoading = false;
        });
    }
  });
}

const getTaskStatus = () => {
  queryTaskStatus().then((response) => {
    hasTaskList.value = response.data;
  });
};

const getSourceList = (data) => {
  sourceList.value = data.dataList;
  pSimRange.value = data.pSimRange;
  updateMapKey.value++;
};

const updateTaskStatus = () => {
  console.log("updateTaskStatus");
  getTaskStatus();
};

// 执行逻辑
const handleRun = () => {
  proxy.$refs["queryRef"].validate((valid) => {
    if (valid) {
      queryParams.value.btnLoading = true;
      queryRunTask(queryParams.value.dataForm)
        .then((response) => {
          // getSourceList(response.data.data);
          // runRusultData.value = response.data;
          // updateTableKey.value++;
          proxy.$modal.msgSuccess("算法已执行，请稍后在数据信息点击刷新查看");
        })
        .finally(() => {
          getTaskStatus();
          queryParams.value.btnLoading = false;
        });
    }
  });
};

onMounted(() => {
  handleReset();
  getTaskStatus();
});
</script>

<style scoped lang="scss">
:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

:deep(.el-form-item--large .el-form-item__label) {
  font-size: 14px;
  font-weight: normal;
  // line-height: 24px;
}

:deep(.el-form-item__label) {
  line-height: 36px; /* 正常行高 */
  white-space: normal; /* 允许换行 */
  word-break: break-all;
}

:deep(.v-line-24 .el-form-item__label) {
  line-height: 22px;
}

.drag-upload {
  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 216px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 200px;

      .el-icon--upload {
        margin-top: 0px;
      }

      .el-icon {
        height: 1.2em;
        width: 1.2em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 16px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }
}

.dialog-footer {
  margin-top: 30px;
}
</style>

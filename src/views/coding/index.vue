<template>
  <div class="coming-soon">
    <div class="content">
      <img
        src="@/assets/images/coming-soon.jpg"
        alt="Coming Soon"
        class="image"
      />
      <p>功能正在开发中，敬请期待！</p>
      <!-- <router-link to="/" class="back-home">返回首页</router-link> -->
    </div>
  </div>
</template>

<script setup name="Test1"></script>

<style scoped lang="scss">
.coming-soon {
  display: flex;
  justify-content: center;
  align-items: center;
  // height: 100vh;
  background-color: #fff;
  text-align: center;
}

.content {
  margin-top: 150px;
  padding: 20px;
  background: #fff;
  // box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.image {
  max-width: 100%;
  height: auto;
  margin-bottom: 20px;
}

h1 {
  font-size: 2.5em;
  color: #333;
  margin-bottom: 10px;
}

p {
  font-size: 1.2em;
  color: #666;
  margin-bottom: 20px;
  fweight: 700;
}

.back-home {
  display: inline-block;
  padding: 10px 20px;
  font-size: 1em;
  color: #fff;
  background-color: #409eff;
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.back-home:hover {
  background-color: #66b1ff;
}
</style>

<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form
          size="large"
          :model="queryParams"
          ref="queryRef"
          :inline="false"
        >
          <el-form-item label="" prop="testKey">
            <el-upload
              ref="uploadRef"
              :limit="1"
              class="upload-file"
              accept=".xlsx, .xls"
              :headers="uploadData.headers"
              :action="uploadData.url"
              :disabled="uploadData.isUploading"
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :auto-upload="false"
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将xls、xlsx格式文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>

          <el-form-item v-if="stype == 1" label="" prop="runWay">
            <el-switch
              v-model="queryParams.runWay"
              class="mb-2"
              size="large"
              active-text="运行有向网络"
              inactive-text="运行无向网络"
            />
          </el-form-item>
          <el-form-item v-if="stype == 2" label="聚类方法" prop="classMethod">
            <el-select
              v-model="queryParams.classMethod"
              placeholder="请选择聚类方法"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              style="width: 100%"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="stype == 3"
            label="风险评估指标"
            prop="evaluateMetrics"
          >
            <el-select
              v-model="queryParams.evaluateMetrics"
              placeholder="请选择风险评估指标"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              style="width: 100%"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="stype == 3" label="每次移除数量" prop="batchSize">
            <el-input-number
              v-model="queryParams.batchSize"
              :min="1"
              :max="100000"
              :precision="0"
              style="width: 100%"
              placeholder="请输入每次移除数量"
              :controls="false"
            />
          </el-form-item>

          <el-form-item v-if="stype == 3" label="选择界面语言" prop="language">
            <el-radio-group v-model="queryParams.language">
              <el-radio label="zh">中文</el-radio>
              <el-radio label="en">英文</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <div class="v-flex v-row-center v-m-t-20" style="width: 100%">
              <el-button
                :loading="analysisLoading"
                v-if="stype == 1"
                type="success"
                icon="histogram"
                @click="handleAnalysis"
                >分析运行</el-button
              >
              <el-button
                class="v-m-x-40"
                type="success"
                v-if="stype == 1"
                :loading="resultLoading"
                icon="pointer"
                @click="handleResult"
                >查看结果</el-button
              >
              <el-button
                class="v-m-x-40"
                type="success"
                v-if="stype == 2"
                :loading="classLoading"
                icon="pointer"
                @click="handleClass"
                >开始聚类</el-button
              >

              <el-button
                :loading="analysisLoading"
                v-if="stype == 3"
                type="success"
                icon="histogram"
                @click="handleAnalysis"
                >开始分析</el-button
              >
              <el-button
                class="v-m-x-40"
                type="success"
                v-if="stype == 3"
                :loading="resultLoading"
                icon="download"
                @click="exportResult"
                >导出结果</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="14">
        <el-row class="v-m-t-10">
          <el-card shadow="hover" style="width: 100%">
            <template #header>
              <div class="v-flex v-row-between">
                <div class="v-title">{{ titleCardChart[stype] }}</div>
                <div class="v-pointer v-link">
                  <el-icon><ZoomIn /></el-icon>
                </div>
              </div>
            </template>
            <div
              class="chart-box"
              :style="{ height: stype == 3 ? '510px' : '380px' }"
            >
              <GraphCharts v-if="stype == 1" />
              <DotGraphCharts
                :key="echartsKey"
                @updateData="handleUpdateData"
                v-if="stype == 2"
              />
              <el-carousel
                v-if="stype == 3"
                :interval="4000"
                :autoplay="false"
                :loop="false"
                :motion-blur="true"
                height="510px"
              >
                <el-carousel-item v-for="item in 6" :key="item">
                  <h3 text="2xl" justify="center">{{ item }}</h3>
                </el-carousel-item>
              </el-carousel>
              <el-empty v-if="false" description="暂无分析数据" />
            </div>
          </el-card>
        </el-row>
      </el-col>
    </el-row>

    <el-row v-if="['1', '3'].includes(stype)">
      <el-card shadow="hover" style="width: 100%">
        <template #header>
          <div class="v-flex v-row-between">
            <div class="v-title">结果预览</div>
            <div class="v-pointer v-link">
              <el-icon><Share /></el-icon>
            </div>
          </div>
        </template>
        <div class="result-box">
          <el-empty description="暂无分析数据" />
        </div>
      </el-card>
    </el-row>
  </div>
</template>

<script setup name="Index">
import { getToken } from "@/utils/auth";
import GraphCharts from "@/components/GraphCharts/index";
import DotGraphCharts from "@/components/GraphCharts/dotType";
const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const route = useRoute();
const data = reactive({
  queryParams: {
    runWay: undefined, // 运行方式
    classMethod: undefined, // 聚类方法
    evaluateMetrics: undefined, // 评估指标
    language: "zh", // 语言
  },
  uploadData: {
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData",
  },
  analysisLoading: false,
  resultLoading: false,
  classLoading: false,
  stype: route.query.stype || "1",
  titleCardChart: {
    1: "风险节点中心性分析",
    2: "聚类结果",
    3: "分析结果",
  },
  echartsKey: 0, // 用于刷新echarts
});

const {
  queryParams,
  uploadData,
  analysisLoading,
  resultLoading,
  classLoading,
  stype,
  titleCardChart,
  echartsKey,
} = toRefs(data);

// 风险节点中心性分析 stype == 1  根据stype调不同的接口
const handleAnalysis = () => {
  analysisLoading.value = true;
  // Simulate analysis process
  setTimeout(() => {
    analysisLoading.value = false;
    // Handle analysis result here, such as updating data or UI
  }, 3000);
};

// 查看结果 stype == 1
const handleResult = () => {
  resultLoading.value = true;
  setTimeout(() => {
    resultLoading.value = false;
  }, 2000);
};
// 聚类 stype == 2
const handleClass = () => {
  classLoading.value = true;
  setTimeout(() => {
    classLoading.value = false;
  }, 2000);
};

// 调接口导出结果 stype == 3
const exportResult = () => {
  resultLoading.value = true;
  proxy
    .download(
      "system/exportResult",
      {
        ...queryParams.value,
      },
      `result_${new Date().getTime()}.xlsx`
    )
    .finally(() => {
      resultLoading.value = false;
    });
};

/** 更新聚类数据 */
const handleUpdateData = (data) => {
  // 调接口，更新入参data
  if (type === "pre") {
    // ...
  } else if (type === "next") {
    // ...
  }
};

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {};
</script>

<style scoped lang="scss">
.home {
  min-height: calc(100vh - 170px);
  background: linear-gradient(
      1turn,
      rgba(140, 201, 56, 0.2),
      rgba(140, 201, 56, 0) 20%
    ),
    linear-gradient(180deg, #8dcfeb, #fefeff);

  .upload-file {
    margin: 10px auto;
    width: 100%;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 250px;

      .el-icon--upload {
        margin-top: 10px;
      }

      .el-icon {
        height: 1.5em;
        width: 1.5em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 16px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }

  .chart-box {
    height: 380px;
  }

  .result-box {
    min-height: 320px;
  }
}

:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}
</style>

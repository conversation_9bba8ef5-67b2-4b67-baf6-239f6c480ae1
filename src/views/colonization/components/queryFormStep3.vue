<template>
  <el-form
    size="large"
    class="v-p-y-10"
    style="min-height: 505px"
    :model="queryParams"
    ref="queryRef"
    label-width="100px"
    :inline="false"
  >
    <!-- 第三步筛选 -->
    <el-row :gutter="20">
      <el-form-item class="v-m-y-40" label="模型算法" prop="getway">
        <el-select
          v-model="queryParams.getway"
          placeholder="请选择模型算法"
          :clearable="false"
          style="width: 216px"
        >
          <el-option
            v-for="dict in colonization_sourcedata_getway"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选项一" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项一"
        />
      </el-form-item>
      <el-form-item label="选项二" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项二"
        />
      </el-form-item>
      <el-form-item label="选项三" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项三"
        />
      </el-form-item>
      <el-form-item label="选项一" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项一"
        />
      </el-form-item>
      <el-form-item label="选项二" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项二"
        />
      </el-form-item>
      <el-form-item label="选项三" class="v-m-y-40" prop="speciesName">
        <el-input
          v-model="queryParams.speciesName"
          style="width: 216px"
          placeholder="请输入选项三"
        />
      </el-form-item>
    </el-row>

    <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
      <el-button type="success" class="v-m-r-20" icon="refresh"
        >算法调用</el-button
      >
      <el-button icon="DocumentDelete" @click="handleReset">重置</el-button>
    </div>
  </el-form>
</template>

<script setup name="Colonization">
import { getToken } from "@/utils/auth";
import { queryGenerationData } from "@/api/colonization";
const { proxy } = getCurrentInstance();
const { colonization_database_select, colonization_sourcedata_getway } =
  proxy.useDict(
    "colonization_database_select",
    "colonization_sourcedata_getway"
  );

const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
});
const data = reactive({
  loading: false,
  queryParams: {
    getway: "1",
    database: "GBIF",
    speciesName: "",
    maxNumber: null,
  },
  uploadData: {
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API +
      `/organism/analysisInfo/uploadAnalysisFile`,
  },
});

const { queryParams, uploadData, loading } = toRefs(data);

// 重置
const handleReset = () => {
  const queryObj = {
    0: {
      getway: "1",
      database: "GBIF",
      speciesName: "",
      maxNumber: null,
    },
    1: {
      getway: "1",
      nowWeatherFiles: [],
      tomWeatherFiles: [],
    },
  };
  queryParams.value = queryObj[proxy.active] || {};
};
// watch(
//   () => props.active,
//   (v) => {
//     if (v) {
//       handleReset();
//     }
//   },
//   { immediate: true }
// );

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadData.value.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadData.value.isUploading = true;
  queryParams.value = {
    ...queryParams.value,
    fileId: response?.data?.id,
  };
};

const handleFileExceed = (file, fileList) => {
  proxy.$modal.msgWarning("请先删除已上传的文件");
};

const handelGenerationData = () => {
  if (queryParams.value.getway == 2) {
  }
  loading.value = true;
  const params = {
    speciesName: queryParams.value.speciesName,
    maxNumber: queryParams.value.maxNumber,
  };
  queryGenerationData(params).then((res) => {
    console.log(res);
    loading.value = false;
  });
};
</script>

<style scoped lang="scss">
:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

.drag-upload {
  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 216px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 200px;

      .el-icon--upload {
        margin-top: 0px;
      }

      .el-icon {
        height: 1.2em;
        width: 1.2em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 16px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }
}
</style>

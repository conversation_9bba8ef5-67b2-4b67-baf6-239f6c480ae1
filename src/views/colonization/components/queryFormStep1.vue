<template>
  <el-form
    size="large"
    class="v-p-y-10"
    style="min-height: 505px"
    :model="queryParams"
    ref="queryRef"
    label-width="100px"
    :inline="false"
  >
    <el-form-item class="v-m-y-40" label="获取方式" prop="getway">
      <el-select
        v-model="queryParams.getway"
        placeholder="请选择获取方式"
        :clearable="false"
        style="width: 100%"
      >
        <el-option
          v-for="dict in colonization_sourcedata_getway"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 2"
      label=""
      class="drag-upload"
      prop="testKey"
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        class="upload-file"
        name="dataFile"
        accept=".xlsx, .xls"
        limit="1"
        :headers="uploadData.headers"
        :action="uploadData.url"
        auto-upload
        :on-remove="handleFileRemove"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-exceed="handleFileExceed"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将xls、xlsx格式文件拖到此处，或<em>点击上传</em>
        </div>
      </el-upload>
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 1"
      label="数据库"
      class="v-m-y-40"
      prop="database"
    >
      <el-select
        v-model="queryParams.database"
        placeholder="请选择数据库"
        :clearable="false"
        style="width: 100%"
      >
        <el-option
          v-for="dict in colonization_database_select"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 1"
      label="文件名称"
      class="v-m-y-40"
      prop="speciesName"
    >
      <el-input
        v-model="queryParams.speciesName"
        style="width: 100%"
        placeholder="请输入文件名称"
      />
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 1"
      label="获取数据量"
      class="v-m-b-60"
      prop="maxNumber"
    >
      <el-input-number
        v-model="queryParams.maxNumber"
        :min="1"
        :max="100000"
        :precision="0"
        style="width: 100%"
        placeholder="请输入每次移除数量"
        :controls="false"
      />
    </el-form-item>

    <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
      <el-button
        type="success"
        class="v-m-r-20"
        icon="refresh"
        :disabled="queryParams.getway == 2"
        :loading="loading"
        @click="handelGenerationData"
        >同步</el-button
      >
      <el-button
        :disabled="queryParams.getway == 2"
        icon="DocumentDelete"
        @click="handleReset"
        >重置</el-button
      >
    </div>
  </el-form>
</template>

<script setup name="Colonization">
import { getToken } from "@/utils/auth";
import { queryGenerationData, removeGenerationFile } from "@/api/colonization";
const { proxy } = getCurrentInstance();
const { colonization_database_select, colonization_sourcedata_getway } =
  proxy.useDict(
    "colonization_database_select",
    "colonization_sourcedata_getway"
  );
const emit = defineEmits();
const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
});
const data = reactive({
  loading: false,
  queryParams: {
    getway: "1",
    database: "GBIF",
    speciesName: "",
    maxNumber: null,
  },
  uploadData: {
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API + `/system/generationData/data/upload`,
  },
});

const { queryParams, uploadData, loading } = toRefs(data);

// 重置
const handleReset = () => {
  if (queryParams.value.getway == 2) {
    return;
  }
  const queryObj = {
    0: {
      getway: "1",
      database: "GBIF",
      speciesName: "",
      maxNumber: null,
    },
    1: {
      getway: "1",
      nowWeatherFiles: [],
      tomWeatherFiles: [],
    },
  };
  queryParams.value = queryObj[proxy.active] || {};
};
// watch(
//   () => props.active,
//   (v) => {
//     if (v) {
//       handleReset();
//     }
//   },
//   { immediate: true }
// );

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadData.value.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadData.value.isUploading = true;
  file.data = response.data;
  emit("updateList", response.data.id);
};
/** 文件删除处理 */
const handleFileRemove = (file, fileList) => {
  uploadData.value.isUploading = true;
  console.log(file, fileList);
  const params = {
    id: file.data.id,
    fileId: file.data?.uploadFile[0]?.id,
  };
  removeGenerationFile(params).then((res) => {
    console.log(res);
    emit("updateList", response.data.id);
    proxy.$modal.msgSuccess("删除成功");
  });
};

const handleFileExceed = (file, fileList) => {
  proxy.$modal.msgWarning("请先删除已上传的文件");
};

const handelGenerationData = () => {
  if (queryParams.value.getway == 2) {
    return;
  }
  loading.value = true;
  const params = {
    speciesName: queryParams.value.speciesName,
    maxNumber: queryParams.value.maxNumber,
  };
  queryGenerationData(params).then((res) => {
    console.log(res);
    loading.value = false;
    emit("updateList", res.data.id);
  });
};
</script>

<style scoped lang="scss">
:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

.drag-upload {
  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 216px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 200px;

      .el-icon--upload {
        margin-top: 0px;
      }

      .el-icon {
        height: 1.2em;
        width: 1.2em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 14px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }
}
</style>

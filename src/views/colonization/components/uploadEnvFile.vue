<template>
  <div class="upload-file">
    <el-upload
      :multiple="multiple"
      :action="uploadFileUrl"
      :auto-upload="!multiple"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :name="uploadFileName"
      :on-change="handleFileChange"
      :on-remove="handleDelete"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="true"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUpload"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">{{ t("upload.selectFile") }}</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="v-flex">
      <div class="el-upload__tip" v-if="showTip">
        {{ t("upload.pleaseUpload") }}
        <template v-if="fileSize">
          {{ t("upload.sizeLimit") }}
          <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          {{ t("upload.formatLimit") }}
          <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        {{ t("upload.fileText") }}
      </div>
      <el-button
        v-if="templateUrl"
        class="download v-m-t-8"
        size="small"
        @click="handeldownloadTemplate"
        type="text"
        >{{ t("upload.downloadTemplate") }}</el-button
      >
    </div>
    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
        v-for="(file, index) in fileList"
      >
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link
            :underline="false"
            @click="handleDelete(index)"
            type="danger"
            >{{ t("upload.delete") }}</el-link
          >
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { t } from "@/utils/i18n";

import { removeFile } from "@/api/colonization";
import { removeCommonFile } from "@/api/colonization";
const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 1,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 0,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "xlsx", "ppt", "txt", "pdf"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  uploadUrl: {
    type: String,
    default: "/system/oss/upload",
  },
  removeUrl: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "modernUploadFile",
  },
  templateUrl: {
    type: String,
    default: "",
  },
  templateParams: {
    type: Object,
    default: () => {},
  },
  uploadFileName: {
    type: String,
    default: "dataFile",
  },
  isNeedformatId: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  otherFileId: {
    type: String,
    default: "",
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;

// 批量上传队列处理
const uploadQueue = ref([]);
const isUploading = ref(false);
const lastUploadId = ref(null);

const uploadFileUrl = computed(() => {
  let url = baseUrl + props.uploadUrl;
  const separator = url.includes("?") ? "&" : "?";
  if (props.otherFileId?.length && !url.includes("id=")) {
    url = `${url}${separator}id=${props.otherFileId}`;
    return url;
  }
  // 如果是批量上传且有上一个文件的ID，拼接到URL
  if (props.multiple && lastUploadId.value && !url.includes("id=")) {
    url = `${url}${separator}id=${lastUploadId.value}`;
    return url;
  }

  return url;
});

const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

// watch(
//   () => props.modelValue,
//   async (val) => {
//     if (val) {
//       let temp = 1;
//       // 首先将值转为数组
//       let list;
//       if (Array.isArray(val)) {
//         list = val;
//       } else {
//         await listByIds(val).then((res) => {
//           list = res.data.map((oss) => {
//             oss = { name: oss.originalName, url: oss.url, ossId: oss.ossId };
//             return oss;
//           });
//         });
//       }
//       // 然后将数组转为对象数组
//       fileList.value = list.map((item) => {
//         item = { name: item.name, url: item.url, ossId: item.ossId };
//         item.uid = item.uid || new Date().getTime() + temp++;
//         return item;
//       });
//     } else {
//       fileList.value = [];
//       return [];
//     }
//   },
//   { deep: true, immediate: true }
// );

// 文件变化处理（批量上传队列）
function handleFileChange(file, fileList) {
  if (props.multiple && file && file.raw && file.status === "ready") {
    // 添加到上传队列
    uploadQueue.value.push(file.raw);

    // 如果当前没有在上传，开始队列上传
    if (!isUploading.value) {
      startQueueUpload(file);
    }
  }
}

// 开始队列上传
async function startQueueUpload(file) {
  if (uploadQueue.value.length === 0 || isUploading.value) return;

  isUploading.value = true;

  while (uploadQueue.value.length > 0) {
    const file = uploadQueue.value.shift();
    const res = await uploadSingleFile(file);
    file.fileId = res.fileId;
    file.id = res.id;
  }

  isUploading.value = false;
  lastUploadId.value = null; // 重置ID

  // 触发更新事件
  if (props.isNeedformatId) {
    emit(
      "update:modelValue",
      uploadList.value[uploadList.value.length - 1]?.ossId
    );
  } else {
    emit("update:modelValue", uploadList.value);
  }
}

// 上传单个文件
function uploadSingleFile(file) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append(props.uploadFileName, file);

    const xhr = new XMLHttpRequest();
    xhr.open("POST", uploadFileUrl.value);
    xhr.setRequestHeader("Authorization", headers.value.Authorization);

    xhr.onload = function () {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        if (response.code === 200) {
          // 保存ID用于下次上传
          lastUploadId.value = response.data?.id;

          // 添加到上传列表
          uploadList.value.push({
            name: response.data[props.type]?.[uploadList?.value?.length]?.name,
            url: response.data?.url,
            ossId: response.data?.id,
            fileId: response.data[props.type]?.[uploadList?.value?.length]?.id,
          });
          console.log("💣💣💣💣", uploadFileUrl.value, response.data);
          resolve({
            fileId: uploadFileUrl.value.includes("system/envData/upload/future")
              ? response.data?.futureZip
              : response.data[props.type]?.[uploadList?.value?.length - 1]?.id,
            id: response.data?.id,
          });
        } else {
          reject(response);
        }
      } else {
        reject(new Error("Upload failed"));
      }
    };

    xhr.onerror = function () {
      reject(new Error("Upload failed"));
    };

    xhr.send(formData);
  });
}

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split(".");
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(
        t("upload.formatError", { formats: props.fileType.join("/") })
      );
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(t("upload.sizeError", { size: props.fileSize }));
      return false;
    }
  }
  proxy.$modal.loading(t("upload.uploading"));
  number.value++;
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(t("upload.limitError", { limit: props.limit }));
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError(t("upload.uploadFailed"));
}

// 上传成功回调（仅用于非批量上传）
function handleUploadSuccess(res, file) {
  // 如果是批量上传，由队列处理，这里不处理
  if (props.multiple) return;

  if (res.code === 200) {
    uploadList.value.push({
      name: res.data.fileName,
      url:
        res.data[props.type]?.[uploadList?.value?.length]?.id ?? res.data?.url,
      ossId: res.data?.id,
    });
    if (props.isNeedformatId) {
      emit("update:modelValue", res.data?.id);
    } else {
      emit("update:modelValue", uploadList.value);
    }

    proxy.$modal.closeLoading();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.fileUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除文件
function handleDelete(file) {
  console.log(file, "file");
  let ossId =
    file?.response?.data[props.type]?.[0]?.id ||
    file?.response?.data?.id ||
    file?.raw?.fileId;

  if (props.removeUrl) {
    removeFile(props.removeUrl, {
      fileId: ossId,
      id: file?.response?.data?.id || file?.raw?.id,
    });
  } else {
    removeCommonFile(ossId);
  }

  // uploadList.value.splice(index, 1);
  const index = uploadList.value.findIndex((f) => f.url === ossId);
  uploadList.value.splice(index, 1);

  emit("update:modelValue", uploadList.value?.[0]?.ossId);

  // emit("update:modelValue", listToString(fileList.value));
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value
      .filter((f) => f.url !== undefined)
      .concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", listToString(fileList.value));
    proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name = "") {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return name;
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].ossId) {
      strs += list[i].ossId + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}

function handeldownloadTemplate() {
  const templateName = {
    1: "预训练文件模板",
    2: "微调文件模板",
    3: "预测文件模板",
    4: "未来环境数据",
    5: "发生数据需优化数据",
    6: "发生数据最终数据模板",
  }[props.templateParams?.type || "1"];
  proxy.download(
    props.templateUrl,
    props.templateParams,
    `${templateName}.csv`
  );
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
// :deep(.el-upload-list) {
//   max-height: 89px;
//   overflow-y: scroll;
// }
</style>

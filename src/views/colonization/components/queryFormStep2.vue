<template>
  <el-form
    size="large"
    class="v-p-y-10"
    style="min-height: 505px"
    :model="queryParams"
    ref="queryRef"
    label-width="100px"
    :inline="[0, 1].includes(active) ? false : true"
  >
    <!-- 第二步 筛选 -->
    <el-form-item class="v-m-t-20" label="获取方式" prop="getway">
      <el-select
        v-model="queryParams.getway"
        placeholder="请选择获取方式"
        :clearable="false"
        style="width: 100%"
      >
        <el-option
          v-for="dict in colonization_envdata_getway"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item
      label="现代气候数据"
      prop="nowWeatherFiles"
      v-if="queryParams.getway == 2"
    >
      <uploadEnvFile
        :fileType="['xls', 'xlsx']"
        uploadUrl="/system/envData/upload/modern"
        removeUrl="/system/envData/upload/modern/remove"
        v-model="queryParams.nowWeatherFiles"
        type="modernUploadFile"
      />
    </el-form-item>
    <el-form-item
      label="未来气候数据"
      prop="tomWeatherFiles"
      v-if="queryParams.getway == 2"
    >
      <uploadEnvFile
        :fileType="['xls', 'xlsx']"
        uploadUrl="/system/envData/upload/future"
        removeUrl="/system/envData/upload/future/remove"
        v-model="queryParams.tomWeatherFiles"
        type="futureUploadFile"
      />
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 1"
      label="现代气候数据"
      class="v-m-y-60"
      prop="nowWeatherFiles"
    >
      <el-cascader
        v-model="queryParams.nowWeatherFiles"
        :options="nowWeatherOptions"
        filterable
        collapse-tags
        placeholder="请选择现代气候数据"
        style="width: 100%"
        :props="{
          multiple: true,
          label: 'name',
          value: 'id',
          checkStrictly: true, // 是否严格的遵守父子节点不互相关联
        }"
        clearable
      />
    </el-form-item>

    <el-form-item
      v-if="queryParams.getway == 1"
      label="未来气候数据"
      class="v-m-y-60"
      prop="getway"
    >
      <el-cascader
        v-model="queryParams.futureWeatherFiles"
        :options="tomWeatherOptions"
        filterable
        placeholder="请选择未来气候数据"
        style="width: 100%"
        collapse-tags
        :props="{
          multiple: true,
          label: 'name',
          value: 'id',
          checkStrictly: false,
        }"
        clearable
      />
    </el-form-item>

    <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
      <el-button
        type="success"
        class="v-m-r-20"
        icon="refresh"
        :loading="loading"
        @click="handelGenerationData"
        >同步</el-button
      >
      <el-button icon="DocumentDelete" @click="handleReset">重置</el-button>
    </div>
  </el-form>
</template>

<script setup name="Colonization">
import { getToken } from "@/utils/auth";
import { queryGenerationData, queryEnvFileTree } from "@/api/colonization";
const { proxy } = getCurrentInstance();
import uploadEnvFile from "./uploadEnvFile.vue";
const { colonization_database_select, colonization_envdata_getway } =
  proxy.useDict("colonization_database_select", "colonization_envdata_getway");

const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
});
const data = reactive({
  loading: false,
  queryParams: {
    getway: "1",
    database: "GBIF",
    speciesName: "",
    maxNumber: null,
  },
  uploadData: {
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API +
      `/organism/analysisInfo/uploadAnalysisFile`,
  },
  nowWeatherOptions: [],
  tomWeatherOptions: [],
});

const {
  queryParams,
  uploadData,
  loading,
  nowWeatherOptions,
  tomWeatherOptions,
} = toRefs(data);

// 重置
const handleReset = () => {
  const queryObj = {
    0: {
      getway: "1",
      database: "GBIF",
      speciesName: "",
      maxNumber: null,
    },
    1: {
      getway: "1",
      nowWeatherFiles: [],
      tomWeatherFiles: [],
    },
  };
  queryParams.value = queryObj[proxy.active] || {};
};

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadData.value.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadData.value.isUploading = true;
  queryParams.value = {
    ...queryParams.value,
    fileId: response?.data?.id,
  };
};

const handleFileExceed = (file, fileList) => {
  proxy.$modal.msgWarning("请先删除已上传的文件");
};

const handelGenerationData = () => {
  if (queryParams.value.getway == 2) {
  }
  loading.value = true;
  const params = {
    speciesName: queryParams.value.speciesName,
    maxNumber: queryParams.value.maxNumber,
  };
  queryGenerationData(params).then((res) => {
    console.log(res);
    loading.value = false;
  });
};

const getEnvFileTree = () => {
  Promise.all([
    queryEnvFileTree({ envFileType: "MODERN" }),
    queryEnvFileTree({ envFileType: "FUTURE" }),
  ]).then((res) => {
    nowWeatherOptions.value = res[0].data;
    tomWeatherOptions.value = res[1].data;
  });
};

onMounted(() => {
  getEnvFileTree();
});
</script>

<style scoped lang="scss">
:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

.drag-upload {
  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 216px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 200px;

      .el-icon--upload {
        margin-top: 0px;
      }

      .el-icon {
        height: 1.2em;
        width: 1.2em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 16px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }
}
</style>

<template>
  <div class="app-container1">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Download"
          @click="
            handleExport(
              `/system/generationData/dataDownload?generateDataId=${dataId}`
            )
          "
          >{{ t("colonization.exportOccurrenceData") }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Download"
          @click="handleExport(`/organism/algorithmInfo/biomodDownload`)"
        >
          下载Biomod2算法结果
          <!-- {{ t("colonization.exportColonizationData") }} -->
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" border :data="speciesList">
      <el-table-column
        :label="t('colonization.speciesName')"
        align="center"
        prop="scientificName"
      />
      <el-table-column
        :label="t('colonization.longitude')"
        align="center"
        prop="decimalLongitude"
      />
      <el-table-column
        :label="t('colonization.latitude')"
        align="center"
        prop="decimalLatitude"
      />
      <el-table-column
        :label="t('colonization.modifiedTime')"
        align="center"
        prop="modified"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.modified, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty :image-size="200" :description="t('colonization.noData')" />
      </template>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Species">
import { generationDataList } from "@/api/colonization";
import { t } from "@/utils/i18n";

const props = defineProps({
  dataId: {
    type: String,
    default: "",
  },
  downloadApi: {
    type: String,
    default: "/system/generationData/data/export/csv",
  },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(["getSourceList", "updateTaskStatus"]);

const speciesList = ref([]);
const loading = ref(false);
const total = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

/** 查询列表 */
function getList() {
  if (!props.dataId) {
    speciesList.value = [];
    total.value = 0;
    return;
  }
  loading.value = true;
  const params = {
    ...queryParams.value,
    id: props.dataId,
  };

  generationDataList(params)
    .then((response) => {
      speciesList.value = response.rows;
      total.value = response.total;
      loading.value = false;

      // 触发地图数据更新事件
      if (response.rows && response.rows.length > 0) {
        emit("getSourceList", {
          dataList: response.rows,
          pSimRange: { min: 0, max: 1 }, // 可以根据实际数据计算
        });
      }
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 导出按钮操作 */
function handleExport(downloadUrl) {
  if (downloadUrl.includes("?generateDataId=") && !props.dataId) {
    proxy.$modal.msgError("请同步发生数据！");
    return;
  }
  proxy.download(
    downloadUrl,
    {
      ...queryParams.value,
    },
    `generation_data_${new Date().getTime()}.xlsx`
  );
}

// 监听 dataId 变化
watch(
  () => props.dataId,
  () => {
    getList();
  },
  { immediate: true }
);

onMounted(() => {
  getList();
});
</script>

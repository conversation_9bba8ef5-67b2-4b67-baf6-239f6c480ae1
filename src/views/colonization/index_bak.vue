<template>
  <div class="page-container v-p-20">
    <el-card shadow="always">
      <!-- <template #header>
        <div class="card-header">
          <span>定殖适生风险分析</span>
        </div>
      </template> -->
      <el-steps
        align-center
        style="max-width: 100%"
        process-status="finish"
        :active="active"
        class="v-m-t-20"
      >
        <el-step
          class="v-pointer"
          @click="active = 0"
          title="获取发生数据"
        ></el-step>
        <el-step
          class="v-pointer"
          @click="active = 1"
          title="获取环境数据"
        ></el-step>
        <el-step
          class="v-pointer"
          @click="active = 2"
          title="建模分析"
        ></el-step>
      </el-steps>
    </el-card>

    <el-row :gutter="20" class="v-m-y-20">
      <el-col :span="[0, 1].includes(active) ? 8 : 24">
        <el-card shadow="always">
          <queryFormStep1
            @updateList="(val) => (dataId = val)"
            v-show="active == 0"
          ></queryFormStep1>
          <queryFormStep2 v-show="active == 1"></queryFormStep2>
          <queryFormStep3 v-if="active == 2"></queryFormStep3>
        </el-card>
      </el-col>
      <el-col :span="16" v-if="[0, 1].includes(active)">
        <el-card shadow="always">
          <spreadMap height="504px" />
        </el-card>
      </el-col>
    </el-row>
    <el-card shadow="always">
      <template #header>
        <div class="card-header">
          <span>结果预览</span>
        </div>
      </template>

      <tableList :dataId="dataId" v-if="[0, 1].includes(active)" />
      <spreadMap height="504px" v-else />
    </el-card>

    <div class="v-flex v-row-center v-m-t-40 v-m-b-30">
      <el-button
        type="success"
        class="v-m-r-20"
        icon="check"
        @click="active < 3 && active++"
      >
        下一步</el-button
      >
      <el-button icon="DocumentDelete" @click="handleReset">取消</el-button>
    </div>
  </div>
</template>

<script setup name="Colonization">
import spreadMap from "@/components/Map/index";
import tableList from "./components/tableList.vue";
import queryFormStep1 from "./components/queryFormStep1.vue";
import queryFormStep2 from "./components/queryFormStep2.vue";
import queryFormStep3 from "./components/queryFormStep3.vue";
import useSettingsStore from "@/store/modules/settings";
const { proxy } = getCurrentInstance();
const theme = computed(() => useSettingsStore().theme);
const data = reactive({
  active: 0,
  dataId: "",
});

const { active, dataId } = toRefs(data);

function activeStyle(step) {
  if (active.value != step) return {};
  // return {
  //   "background-color": theme.value,
  //   "border-color": theme.value,
  //   color: "#fff",
  // };
}
</script>

<style scoped lang="scss">
.steps-title {
  // border: 1px solid #636466;
  // padding: 5px 10px;
}
</style>

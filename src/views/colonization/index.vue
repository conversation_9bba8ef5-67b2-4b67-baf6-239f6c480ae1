<template>
  <div class="page-container v-p-20">
    <el-row :gutter="20">
      <el-col :span="10">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("colonization.algorithmModel") }}</div>
            </div>
          </template>
          <div class="v-m-l-8">
            <el-form
              size="large"
              class="v-p-y-10"
              :model="queryParams.dataForm"
              ref="step1FormRef"
              label-width="110px"
              :inline="false"
              :rules="formRules"
            >
              <!-- 获取发生数据 -->
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Document /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("colonization.getOccurrenceData") }}
                  </div>
                </div>
                <el-icon
                  @click="layoutSetting.showBasic = !layoutSetting.showBasic"
                  class="v-pointer"
                  v-show="layoutSetting.showBasic"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="layoutSetting.showBasic = !layoutSetting.showBasic"
                  class="v-pointer"
                  v-show="!layoutSetting.showBasic"
                  ><ArrowUp
                /></el-icon>
              </div>

              <div v-show="layoutSetting.showBasic">
                <!-- 获取方式选择 -->
                <el-form-item
                  class="v-m-y-20"
                  :label="t('colonization.getway')"
                  prop="getway"
                >
                  <el-select
                    v-model="queryParams.dataForm.getway"
                    :placeholder="t('colonization.getwayPlaceholder')"
                    :clearable="false"
                    style="width: 100%"
                    @change="handleStep1GetwayChange"
                  >
                    <el-option
                      v-for="dict in colonization_sourcedata_getway"
                      :key="dict.value"
                      :label="getDictLabel(dict)"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 文件上传方式 -->
                <el-form-item
                  v-if="queryParams.dataForm.getway == 2"
                  :label="t('colonization.uploadFile')"
                  class="drag-upload"
                  prop="uploadFile"
                >
                  <el-upload
                    ref="uploadRef"
                    :limit="1"
                    class="upload-file"
                    name="dataFile"
                    accept=".xlsx, .xls, .csv"
                    limit="1"
                    :headers="uploadData.headers"
                    :action="uploadData.url"
                    auto-upload
                    :on-remove="handleFileRemove"
                    :on-progress="handleFileUploadProgress"
                    :on-success="handleFileSuccess"
                    :on-exceed="handleFileExceed"
                    drag
                  >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                      <!-- {{ t("colonization.uploadFileTip") }} -->
                      将 xls、xlsx、csv 格式文件拖到此处，或点击上传<span
                        style="color: #f56c6c"
                        >（注意:数据优化后无法直接用于算法执行，请按照最终数据模版上传）</span
                      >
                      <el-link
                        class="v-font-12"
                        :underline="false"
                        type="primary"
                        @click="handeldownloadTemplate(5)"
                        >下载需优化数据模板</el-link
                      >、
                      <el-link
                        class="v-font-12"
                        :underline="false"
                        type="primary"
                        @click="handeldownloadTemplate(6)"
                        >下载最终数据模板</el-link
                      >
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 数据库获取方式 -->

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      v-if="queryParams.dataForm.getway == 1"
                      :label="t('colonization.database')"
                      class="v-m-t-20"
                      prop="database"
                    >
                      <el-select
                        v-model="queryParams.dataForm.database"
                        :placeholder="t('colonization.databasePlaceholder')"
                        :clearable="false"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="dict in colonization_database_select"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      v-if="queryParams.dataForm.getway == 1"
                      :label="t('colonization.speciesName')"
                      class="v-m-t-20"
                      prop="speciesName"
                    >
                      <el-input
                        v-model="queryParams.dataForm.speciesName"
                        style="width: 100%"
                        :placeholder="t('colonization.speciesNamePlaceholder')"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      v-if="queryParams.dataForm.getway == 1"
                      :label="t('colonization.dataAmount')"
                      class="v-m-y-20"
                      prop="maxNumber"
                    >
                      <el-input-number
                        v-model="queryParams.dataForm.maxNumber"
                        :min="1"
                        :max="100000"
                        :precision="0"
                        style="width: 100%"
                        :placeholder="t('colonization.dataAmountPlaceholder')"
                        :controls="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 获取发生数据操作按钮 -->
              <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
                <el-button
                  type="success"
                  icon="refresh"
                  v-if="queryParams.dataForm.getway == 1"
                  :loading="step1Loading"
                  @click="handleStep1Sync"
                  >{{ t("colonization.sync") }}</el-button
                >
                <el-button
                  v-if="queryParams.dataForm.getway == 1"
                  icon="DocumentDelete"
                  @click="handleStep1Reset"
                  >{{ t("colonization.reset") }}</el-button
                >
                <el-button
                  icon="Refresh"
                  @click="handleStep1Optimize"
                  :disabled="!queryParams.dataForm.generationDataId"
                  >{{ t("colonization.optimize") }}</el-button
                >
              </div>
            </el-form>
            <!-- 获取环境数据 -->

            <el-form
              size="large"
              class="v-p-y-10"
              :model="queryParams.dataForm"
              ref="step2FormRef"
              label-width="110px"
              :inline="false"
              :rules="formRules"
            >
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20 v-m-t-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Menu /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("colonization.getEnvironmentData") }}
                  </div>
                </div>
                <el-icon
                  @click="
                    layoutSetting.showModelParam = !layoutSetting.showModelParam
                  "
                  class="v-pointer"
                  v-show="layoutSetting.showModelParam"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="
                    layoutSetting.showModelParam = !layoutSetting.showModelParam
                  "
                  class="v-pointer"
                  v-show="!layoutSetting.showModelParam"
                  ><ArrowUp
                /></el-icon>
              </div>

              <div v-show="layoutSetting.showModelParam">
                <!-- Step 2: 获取环境数据表单项 -->
                <el-form-item
                  class="v-m-t-20"
                  :label="t('colonization.getway')"
                  prop="envGetway"
                >
                  <el-select
                    v-model="queryParams.dataForm.envGetway"
                    :placeholder="t('colonization.getwayPlaceholder')"
                    :clearable="false"
                    style="width: 100%"
                    @change="handleStep2GetwayChange"
                  >
                    <el-option
                      v-for="dict in colonization_envdata_getway"
                      :key="dict.value"
                      :label="getDictLabel(dict)"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 文件上传方式 -->
                <el-form-item
                  :label="t('colonization.nowWeatherFiles')"
                  prop="modernLocalFile"
                  v-if="queryParams.dataForm.envGetway == 2"
                >
                  <uploadEnvFile
                    :fileType="['tif', 'tiff']"
                    :uploadUrl="modernUploadUrl"
                    removeUrl="/system/envData/upload/modern/remove"
                    v-model="queryParams.dataForm.modernLocalFile"
                    type="modernUploadFile"
                    :otherFileId="queryParams.dataForm.futureLocalFile"
                    :isNeedformatId="true"
                    :limit="50"
                    :multiple="true"
                  />
                </el-form-item>
                <el-form-item
                  :label="t('colonization.futureWeatherFiles')"
                  prop="futureLocalFile"
                  v-if="queryParams.dataForm.envGetway == 2"
                >
                  <uploadEnvFile
                    :fileType="['zip']"
                    :uploadUrl="futureUploadUrl"
                    removeUrl="/system/envData/upload/future/remove"
                    templateUrl="organism/algorithmInfo/template/download"
                    v-model="queryParams.dataForm.futureLocalFile"
                    :otherFileId="queryParams.dataForm.modernLocalFile"
                    type="futureUploadFile"
                    :templateParams="{ type: 4 }"
                    :isNeedformatId="true"
                    :limit="1"
                    :multiple="true"
                  />
                </el-form-item>

                <!-- 数据库获取方式 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      v-if="queryParams.dataForm.envGetway == 1"
                      :label="t('colonization.nowWeatherFiles')"
                      class="v-m-y-10"
                      prop="modernLocalFile"
                    >
                      <el-cascader
                        v-model="queryParams.dataForm.modernLocalFile"
                        :options="nowWeatherOptions"
                        filterable
                        collapse-tags
                        :placeholder="
                          t('colonization.nowWeatherFilesPlaceholder')
                        "
                        style="width: 100%"
                        :props="{
                          multiple: true,
                          label: 'name',
                          value: 'id',
                          checkStrictly: true,
                        }"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      v-if="queryParams.dataForm.envGetway == 1"
                      :label="t('colonization.futureWeatherFiles')"
                      class="v-m-y-10"
                      prop="futureWeatherFiles"
                    >
                      <el-cascader
                        v-model="queryParams.dataForm.futureWeatherFiles"
                        :options="tomWeatherOptions"
                        filterable
                        :placeholder="
                          t('colonization.futureWeatherFilesPlaceholder')
                        "
                        style="width: 100%"
                        collapse-tags
                        :props="{
                          multiple: true,
                          label: 'name',
                          value: 'id',
                          checkStrictly: false,
                        }"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 获取环境数据操作按钮 -->
              <div class="v-flex v-row-center v-m-t-40" style="width: 100%">
                <el-button
                  type="success"
                  icon="refresh"
                  :loading="step2Loading"
                  v-if="queryParams.dataForm.envGetway == 1"
                  @click="handleStep2Sync"
                  >{{ t("colonization.sync") }}</el-button
                >
                <el-button
                  v-if="queryParams.dataForm.envGetway == 1"
                  icon="DocumentDelete"
                  @click="handleStep2Reset"
                  >{{ t("colonization.reset") }}</el-button
                >

                <el-button
                  icon="Refresh"
                  @click="handleStep2Optimize"
                  :disabled="
                    (queryParams.dataForm.envGetway == 1 &&
                      !queryParams.dataForm.envDataId) ||
                    (queryParams.dataForm.envGetway == 2 &&
                      !queryParams?.dataForm?.modernLocalFile?.length)
                  "
                  :loading="step2Loading"
                  >{{ t("colonization.optimize") }}</el-button
                >

                <el-button
                  icon="Pointer"
                  @click="handleStep2ViewResult"
                  :disabled="
                    (queryParams.dataForm.envGetway == 1 &&
                      !queryParams.dataForm.envDataId) ||
                    (queryParams.dataForm.envGetway == 2 &&
                      !queryParams?.dataForm?.modernLocalFile?.length)
                  "
                  :loading="step2Loading"
                  >{{ t("colonization.viewOptimizationResults") }}</el-button
                >
              </div>
            </el-form>

            <!-- 建模分析 -->
            <el-form
              size="large"
              class="v-p-y-10"
              :model="queryParams.dataForm"
              ref="step3FormRef"
              label-width="136px"
              :inline="false"
              :rules="formRules"
            >
              <div
                style="height: 38px"
                class="v-flex v-m-p-10 v-row-between v-border-bottom v-m-b-20 v-m-t-20"
              >
                <div class="v-flex v-m-p-10">
                  <el-icon><Odometer /></el-icon>
                  <div class="sub-title v-m-l-5">
                    {{ t("colonization.modelingAnalysis") }}
                  </div>
                </div>

                <el-icon
                  @click="
                    layoutSetting.showRoadWeight = !layoutSetting.showRoadWeight
                  "
                  class="v-pointer"
                  v-show="layoutSetting.showRoadWeight"
                  ><ArrowDown
                /></el-icon>
                <el-icon
                  @click="
                    layoutSetting.showRoadWeight = !layoutSetting.showRoadWeight
                  "
                  class="v-pointer"
                  v-show="!layoutSetting.showRoadWeight"
                  ><ArrowUp
                /></el-icon>
              </div>

              <!-- <div class="sub-title v-m-t-80">建模分析</div> -->
              <div v-show="layoutSetting.showRoadWeight">
                <!-- Step 3: 建模分析表单项 -->
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item
                      class="v-m-y-20"
                      :label="t('colonization.modelAlgorithm')"
                      prop="algorithm"
                    >
                      <el-select
                        v-model="queryParams.dataForm.algorithm"
                        :placeholder="
                          t('colonization.modelAlgorithmPlaceholder')
                        "
                        :clearable="false"
                        style="width: 100%"
                        @change="handleAlgorithmChange"
                      >
                        <el-option
                          v-for="dict in colonization_algorithm_type"
                          :key="dict.value"
                          :label="getDictLabel(dict)"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- 算法选择定殖适生模型的时候 -->
                <el-row v-if="queryParams.dataForm.algorithm == 'biomod'">
                  <el-col :span="24">
                    <!-- 在这里完善定殖适生模型的表单 -->
                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item
                          :label="t('colonization.biomodSpeciesName')"
                          class="v-m-t-20"
                          prop="ssName"
                        >
                          <el-input
                            v-model="queryParams.dataForm.ssName"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.biomodSpeciesNamePlaceholder')
                            "
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.pseudoAbsenceRep')"
                          class="v-m-y-20"
                          prop="rep"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.rep"
                            :min="1"
                            :max="100000"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.pseudoAbsenceRepPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.pseudoAbsenceNum')"
                          class="v-m-y-20"
                          prop="absences"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.absences"
                            :min="1"
                            :max="100000"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.pseudoAbsenceNumPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.pseudoAbsenceStrategy')"
                          prop="strategy"
                        >
                          <el-select
                            v-model="queryParams.dataForm.strategy"
                            :placeholder="
                              t('colonization.pseudoAbsenceStrategyPlaceholder')
                            "
                            :clearable="false"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="dict in colonization_strategy"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.modelName')"
                          class="v-m-t-20"
                          prop="moduleDir"
                        >
                          <el-input
                            v-model="queryParams.dataForm.moduleDir"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.modelNamePlaceholder')
                            "
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row
                      :gutter="20"
                      v-if="queryParams.dataForm.strategy == 'disk'"
                    >
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.minDistance')"
                          class="v-m-y-20"
                          prop="diskMin"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.diskMin"
                            :min="1"
                            :max="100000"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.minDistancePlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.maxDistance')"
                          class="v-m-y-20"
                          prop="diskMax"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.diskMax"
                            :min="1"
                            :max="100000"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.maxDistancePlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.modelValidationMethod')"
                          prop="modelStrategy"
                        >
                          <el-select
                            v-model="queryParams.dataForm.modelStrategy"
                            :placeholder="
                              t('colonization.modelValidationMethodPlaceholder')
                            "
                            :clearable="false"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="dict in colonization_model_strategy"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.modelAlgorithms')"
                          prop="models"
                        >
                          <el-select
                            v-model="queryParams.dataForm.models"
                            :placeholder="
                              t('colonization.modelAlgorithmsPlaceholder')
                            "
                            :clearable="false"
                            style="width: 100%"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                          >
                            <el-option
                              v-for="dict in colonization_models"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.modelRepetitions')"
                          class="v-m-y-20"
                          prop="modelRep"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.modelRep"
                            :min="1"
                            :max="10"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.modelRepetitionsPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col
                        :span="12"
                        v-if="queryParams.dataForm.modelStrategy == 'random'"
                      >
                        <el-form-item
                          :label="t('colonization.trainingSetRatio')"
                          class="v-m-y-20"
                          prop="modelPerc"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.modelPerc"
                            :min="0"
                            :max="1"
                            :precision="2"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.trainingSetRatioPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" v-else>
                        <el-form-item
                          :label="t('colonization.kFoldValidation')"
                          class="v-m-y-20"
                          prop="modelK"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.modelK"
                            :min="1"
                            :max="10"
                            :precision="0"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.kFoldValidationPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.modelEvaluationMetrics')"
                          prop="modelEval"
                        >
                          <el-select
                            v-model="queryParams.dataForm.modelEval"
                            :placeholder="
                              t(
                                'colonization.modelEvaluationMetricsPlaceholder'
                              )
                            "
                            :clearable="false"
                            style="width: 100%"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                          >
                            <el-option
                              v-for="dict in colonization_model_eval"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.ensembleMethods')"
                          prop="algo"
                        >
                          <el-select
                            v-model="queryParams.dataForm.algo"
                            :placeholder="
                              t('colonization.ensembleMethodsPlaceholder')
                            "
                            :clearable="false"
                            style="width: 100%"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                          >
                            <el-option
                              v-for="dict in colonization_algo"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.modelSelectionMetrics')"
                          prop="metricSelect"
                        >
                          <el-select
                            v-model="queryParams.dataForm.metricSelect"
                            :placeholder="
                              t('colonization.modelSelectionMetricsPlaceholder')
                            "
                            :clearable="false"
                            style="width: 100%"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                          >
                            <el-option
                              v-for="dict in colonization_model_eval"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item
                          class="v-m-y-20"
                          :label="t('colonization.environmentDataMetrics')"
                          prop="envMetric"
                        >
                          <el-select
                            v-model="queryParams.dataForm.envMetric"
                            :placeholder="
                              t(
                                'colonization.environmentDataMetricsPlaceholder'
                              )
                            "
                            :clearable="false"
                            style="width: 100%"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                          >
                            <el-option
                              v-for="dict in colonization_model_eval"
                              :key="dict.value"
                              :label="getDictLabel(dict)"
                              :value="dict.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row
                      :gutter="20"
                      v-if="queryParams.dataForm.metricSelect?.length"
                    >
                      <el-col :span="12">
                        <el-form-item
                          :label="`${queryParams.dataForm?.metricSelect?.[0]}筛选阈值`"
                          class="v-m-y-20"
                          prop="metricSelectThresh0"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.metricSelectThresh0"
                            :min="0"
                            :max="1"
                            :precision="2"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.selectionThresholdPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col
                        :span="12"
                        v-if="queryParams.dataForm?.metricSelect?.[1]"
                      >
                        <el-form-item
                          :label="`${queryParams.dataForm?.metricSelect?.[1]}筛选阈值`"
                          class="v-m-y-20"
                          prop="metricSelectThresh1"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.metricSelectThresh1"
                            :min="0"
                            :max="1"
                            :precision="2"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.selectionThresholdPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row
                      :gutter="20"
                      v-if="queryParams.dataForm.metricSelect?.length > 2"
                    >
                      <el-col :span="12">
                        <el-form-item
                          :label="`${queryParams.dataForm?.metricSelect?.[2]}筛选阈值`"
                          class="v-m-y-20"
                          prop="metricSelectThresh2"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.metricSelectThresh2"
                            :min="0"
                            :max="1"
                            :precision="2"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.selectionThresholdPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <!-- 算法选择另外两种模型的时候 -->
                <el-row v-else>
                  <el-col :span="24">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item
                          :label="t('colonization.isFinetune')"
                          class="v-m-y-10"
                          prop="isFinetune"
                        >
                          <el-switch
                            class="v-pointer"
                            v-model="queryParams.dataForm.isFinetune"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item
                          label-width="110px"
                          :label="t('colonization.trainingRatio')"
                          class="v-m-y-10"
                          prop="valFraction"
                        >
                          <el-input-number
                            v-model="queryParams.dataForm.valFraction"
                            :min="0"
                            :max="1"
                            style="width: 100%"
                            :placeholder="
                              t('colonization.trainingRatioPlaceholder')
                            "
                            :controls="false"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item
                          :label="t('colonization.pretrainFile')"
                          class="v-m-y-20"
                          prop="pretrainFileId"
                        >
                          <uploadEnvFile
                            :key="updateUploadKey1"
                            :fileType="['xls', 'xlsx', 'csv']"
                            uploadUrl="/organism/commonFile/upload"
                            templateUrl="organism/algorithmInfo/template/download"
                            :templateParams="{ type: 1 }"
                            v-model="queryParams.dataForm.pretrainFileId"
                            uploadFileName="uploadFile"
                            :isNeedformatId="true"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item
                          :label="t('colonization.predictFile')"
                          prop="predictFileId"
                        >
                          <uploadEnvFile
                            :key="updateUploadKey2"
                            :fileType="['xls', 'xlsx', 'csv']"
                            uploadUrl="/organism/commonFile/upload"
                            templateUrl="organism/algorithmInfo/template/download"
                            :templateParams="{ type: 3 }"
                            v-model="queryParams.dataForm.predictFileId"
                            uploadFileName="uploadFile"
                            :isNeedformatId="true"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="24">
                        <el-form-item
                          :label="t('colonization.finetuneFile')"
                          prop="finetuneFileId"
                          v-if="queryParams.dataForm.isFinetune"
                        >
                          <uploadEnvFile
                            :key="updateUploadKey3"
                            :fileType="['xls', 'xlsx', 'csv']"
                            uploadUrl="/organism/commonFile/upload"
                            templateUrl="organism/algorithmInfo/template/download"
                            :templateParams="{ type: 2 }"
                            v-model="queryParams.dataForm.finetuneFileId"
                            uploadFileName="uploadFile"
                            :isNeedformatId="true"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>

              <!-- 建模分析操作按钮 -->
              <div class="v-flex v-row-center v-m-t-60" style="width: 100%">
                <el-button
                  type="success"
                  class="v-m-r-20"
                  icon="refresh"
                  :loading="step3Loading"
                  @click="handleStep3Sync"
                  >{{ t("colonization.algorithmCall") }}</el-button
                >
                <el-button icon="DocumentDelete" @click="handleStep3Reset">{{
                  t("colonization.reset")
                }}</el-button>
              </div>
            </el-form>
          </div>
        </el-card>
      </el-col>

      <el-col :span="14">
        <el-card shadow="always">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("colonization.dataInfo") }}</div>
            </div>
          </template>
          <spreadMap
            :pSimRange="pSimRange"
            :key="updateMapKey"
            :lang="currentLanguage"
            :sourceData="sourceList"
          />
        </el-card>
        <el-card shadow="always" class="v-m-y-20">
          <template #header>
            <div class="card-header">
              <div class="v-title">{{ t("colonization.resultPreview") }}</div>
            </div>
          </template>

          <!-- 根据操作类型显示不同内容 -->
          <!-- 第一步同步/优化操作后显示 tableList -->
          <tableList
            v-if="showResultType === 'tableList'"
            :dataId="queryParams.dataForm.generationDataId"
          />

          <!-- 第三步算法调用后显示 carousel -->
          <div v-else-if="showResultType === 'carousel'" class="carousel">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="Refresh"
                  @click="handelRefresh"
                  :disabled="!queryParams.dataForm.algorithm"
                  >{{ t("colonization.refresh") }}</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="Download"
                  @click="handleDownload"
                  :disabled="!resultTaskId"
                  >{{ t("colonization.downloadResults") }}</el-button
                >
              </el-col>
            </el-row>
            <el-carousel
              v-if="pictureList?.length"
              :interval="4000"
              :autoplay="false"
              :loop="false"
              :motion-blur="true"
              height="510px"
              :key="chartDataGraphKey3"
            >
              <el-carousel-item
                v-for="(item, index) in pictureList"
                :key="index"
                class="v-pointer"
              >
                <el-image
                  ref="imageRef"
                  style="width: 100%; height: 100%"
                  :src="item"
                  :preview-teleported="true"
                  :preview-src-list="pictureList"
                  fit="contain"
                />
              </el-carousel-item>
            </el-carousel>
            <el-empty v-else="false" :description="t('colonization.noData')" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 发生数据优化参数弹框 -->
    <el-dialog
      :title="optimizeParams.title"
      v-model="optimizeParams.open"
      width="500px"
      append-to-body
    >
      <el-form
        ref="optimizeParamsRef"
        :model="optimizeParams.dataForm"
        :rules="optimizeParams.rules"
        label-width="130px"
      >
        <el-form-item
          :label="t('colonization.sparsificationDistance')"
          prop="thinPar"
        >
          <el-input-number
            v-model="optimizeParams.dataForm.thinPar"
            :min="0"
            style="width: 100%"
            :placeholder="t('colonization.sparsificationDistancePlaceholder')"
            :controls="false"
          />
        </el-form-item>
        <el-form-item :label="t('colonization.repeatRunTimes')" prop="reps">
          <el-input-number
            v-model="optimizeParams.dataForm.reps"
            :min="1"
            :precision="0"
            style="width: 100%"
            :placeholder="t('colonization.repeatRunTimesPlaceholder')"
            :controls="false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :loading="optimizeParams.submitLoading"
            type="primary"
            @click="submitOptimizeParams"
            >{{ t("common.confirm") }}</el-button
          >
          <el-button @click="cancelOptimizeParams">{{
            t("common.cancel")
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 环境数据优化参数弹框 -->
    <el-dialog
      :title="envOptimizeParams.title"
      v-model="envOptimizeParams.open"
      width="500px"
      append-to-body
    >
      <el-form
        ref="envOptimizeParamsRef"
        :model="envOptimizeParams.dataForm"
        :rules="envOptimizeParams.rules"
        label-width="120px"
      >
        <el-form-item
          :label="t('colonization.correlationCoefficient')"
          prop="corThreshold"
        >
          <el-input-number
            v-model="envOptimizeParams.dataForm.corThreshold"
            :min="0"
            :max="1"
            :precision="2"
            style="width: 100%"
            :placeholder="t('colonization.correlationCoefficientPlaceholder')"
            :controls="false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :loading="envOptimizeParams.submitLoading"
            type="primary"
            @click="submitEnvOptimizeParams"
            >{{ t("common.confirm") }}</el-button
          >
          <el-button @click="cancelEnvOptimizeParams">{{
            t("common.cancel")
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 环境数据优化结果弹框 -->
    <el-dialog
      :title="envResultDialog.title"
      v-model="envResultDialog.open"
      width="800px"
      append-to-body
      @close="closeEnvResultDialog"
    >
      <div v-if="envResultDialog.data" class="result-content">
        <!-- 使用 JSON 格式展示数据 -->
        <el-card shadow="never" class="result-card">
          <div class="json-display">
            <!-- {{ JSON.stringify(envResultDialog.data, null, 2) }} -->
            <el-button
              type="text"
              v-for="(item, index) in envResultDialog.data?.split(',')"
              :key="index"
            >
              {{ item }}
            </el-button>
          </div>
        </el-card>
      </div>
      <div v-else class="no-data">
        <el-empty :description="t('colonization.noData')" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeEnvResultDialog">{{
            t("common.close")
          }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Spread">
import { getToken } from "@/utils/auth";
import spreadMap from "@/components/Map/AdvancedMap.vue";
import uploadEnvFile from "./components/uploadEnvFile.vue";
import tableList from "./components/tableList.vue";

import {
  queryGenerationData,
  removeGenerationFile,
  queryEnvFileTree,
  generationDataOptimize, // 发生数据的优化接口，如果获取方式选择同步数据也就是1，则同步完拿到ID，可以点优化，作为入参；如果是2，则需要上传文件，上传文件后拿到的ID，可以点优化，作为入参
  envDataOptimize, // 环境数据优化接口
  envDataOptimizeResult, // 查看环境数据优化结果接口
  envDataLocal, // 获取环境数据同步接口
  regressionTaskExecute, // 第三步，建模分析的算法调用接口，如果算法模型选择模型为回归任务则需要调用此接口，如果选择分类任务则需要调用分类任务接口，如果选择定值适生模型则需要调用定值适生模型接口
  classifierList, // 分类任务执行接口
  biomodeling, // 定值适生模型接口
  regressorTaskStatus, // 第三步，建模分析切换模型算法,如果选择回归任务则需要调用此接口，如果选择分类任务则需要调用分类任务接口, 返回结果如果有任务执行，则提示一下，没有任务执行不提示
  classifierTaskStatus, // 第三步，建模分析切换模型算法,如果选择分类任务则需要调用此接口，如果选择回归任务则需要调用回归任务接口, 返回结果如果有任务执行，则提示一下，没有任务执行不提示
  biomodelingTaskStatus, // 第三步，建模分析切换换模型算法，如果选择定殖适生模型 则需要调用此接口，返回结果如果有任务执行，则提示一下，没有任务执行不提示
  regressorTaskResult, // 获取回归任务结果接口，点刷新 handelRefresh 调用，获取的数据结果放到pictureList，渲染轮播图
} from "@/api/colonization";
import { t } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from "@/composables/useLanguageChange";
import {
  ref,
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance,
  computed,
  watch,
  nextTick,
} from "vue";

const { proxy } = getCurrentInstance();

// 语言相关
const languageStore = useLanguageStore();
const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

// 监听语言变化，更新地图和表单验证规则
watch(currentLanguage, () => {
  // 强制更新地图组件
  updateMapKey.value++;

  // 更新表单验证规则但不触发验证
  nextTick(() => {
    formRules.value = getFormRules();
  });
});

// 计算属性：根据语言返回字典项的显示文本
const getDictLabel = (dict) => {
  return currentLanguage.value === "en" ? dict.value : dict.label;
};

// 表单验证规则 - 使用函数返回而不是computed，避免语言切换时触发验证
const getFormRules = () => ({
  // 获取发生数据验证规则
  getway: [
    {
      required: true,
      message: t("colonization.validation.selectGetway"),
      trigger: "change",
    },
  ],
  database: [
    {
      required: true,
      message: t("colonization.validation.selectDatabase"),
      trigger: "change",
    },
  ],
  speciesName: [
    {
      required: true,
      message: t("colonization.validation.enterSpeciesName"),
      trigger: "blur",
    },
  ],
  maxNumber: [
    {
      required: true,
      message: t("colonization.validation.enterDataAmount"),
      trigger: "blur",
    },
  ],
  uploadFile: [
    {
      required: true,
      message: t("colonization.validation.uploadOccurrenceFile"),
      trigger: "change",
    },
  ],

  // 获取环境数据验证规则
  envGetway: [
    {
      required: true,
      message: t("colonization.validation.selectGetway"),
      trigger: "change",
    },
  ],
  modernLocalFile: [
    {
      required: true,
      message: t("colonization.validation.selectModernClimate"),
      trigger: "change",
    },
  ],
  futureLocalFile: [
    {
      required: true,
      message: t("colonization.validation.selectFutureClimate"),
      trigger: "change",
    },
  ],
  futureWeatherFiles: [
    {
      required: true,
      message: t("colonization.validation.selectFutureWeather"),
      trigger: "change",
    },
  ],

  // 建模分析验证规则
  algorithm: [
    {
      required: true,
      message: t("colonization.validation.selectAlgorithm"),
      trigger: "change",
    },
  ],
  valFraction: [
    {
      required: true,
      message: t("colonization.validation.enterTrainingRatio"),
      trigger: "blur",
    },
  ],
  pretrainFileId: [
    {
      required: true,
      message: t("colonization.validation.uploadPretrainFile"),
      trigger: "change",
    },
  ],
  predictFileId: [
    {
      required: true,
      message: t("colonization.validation.uploadPredictFile"),
      trigger: "change",
    },
  ],
  finetuneFileId: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (queryParams.value.dataForm.isFinetune && !value) {
          callback(new Error(t("colonization.validation.uploadFinetuneFile")));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],

  // biomod 定殖适生算法验证规则
  ssName: [
    {
      required: true,
      message: t("colonization.validation.enterSpeciesNameBiomod"),
      trigger: "blur",
    },
  ],
  rep: [
    {
      required: true,
      message: t("colonization.validation.enterPseudoAbsenceRep"),
      trigger: "blur",
    },
  ],
  absences: [
    {
      required: true,
      message: t("colonization.validation.enterPseudoAbsenceNum"),
      trigger: "blur",
    },
  ],
  strategy: [
    {
      required: true,
      message: t("colonization.validation.selectPseudoAbsenceStrategy"),
      trigger: "change",
    },
  ],
  moduleDir: [
    {
      required: true,
      message: t("colonization.validation.enterModelName"),
      trigger: "blur",
    },
  ],
  diskMin: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (queryParams.value.dataForm.strategy === "disk" && !value) {
          callback(new Error(t("colonization.validation.enterMinDistance")));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  diskMax: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (queryParams.value.dataForm.strategy === "disk" && !value) {
          callback(new Error(t("colonization.validation.enterMaxDistance")));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  modelStrategy: [
    {
      required: true,
      message: t("colonization.validation.selectModelValidation"),
      trigger: "change",
    },
  ],
  models: [
    {
      required: true,
      message: t("colonization.validation.selectModelAlgorithms"),
      trigger: "change",
    },
  ],
  modelRep: [
    {
      required: true,
      message: t("colonization.validation.enterModelRepetitions"),
      trigger: "blur",
    },
  ],
  modelPerc: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (queryParams.value.dataForm.modelStrategy === "random" && !value) {
          callback(
            new Error(t("colonization.validation.enterTrainingSetRatio"))
          );
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  modelK: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (queryParams.value.dataForm.modelStrategy !== "random" && !value) {
          callback(
            new Error(t("colonization.validation.enterKFoldValidation"))
          );
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  modelEval: [
    {
      required: true,
      message: t("colonization.validation.selectModelEvaluation"),
      trigger: "change",
    },
  ],
  algo: [
    {
      required: true,
      message: t("colonization.validation.selectEnsembleMethods"),
      trigger: "change",
    },
  ],
  metricSelect: [
    {
      required: true,
      message: t("colonization.validation.selectModelSelection"),
      trigger: "change",
    },
  ],
  envMetric: [
    {
      required: true,
      message: t("colonization.validation.selectEnvironmentData"),
      trigger: "change",
    },
  ],
  metricSelectThresh0: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (
          queryParams.value.dataForm.metricSelect &&
          queryParams.value.dataForm.metricSelect.length > 0 &&
          !value
        ) {
          callback(
            new Error(t("colonization.validation.enterSelectionThreshold"))
          );
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  metricSelectThresh1: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (
          queryParams.value.dataForm.metricSelect &&
          queryParams.value.dataForm.metricSelect.length > 1 &&
          !value
        ) {
          callback(
            new Error(t("colonization.validation.enterSelectionThreshold"))
          );
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  metricSelectThresh2: [
    {
      required: false,
      validator: (_, value, callback) => {
        if (
          queryParams.value.dataForm.metricSelect &&
          queryParams.value.dataForm.metricSelect.length > 2 &&
          !value
        ) {
          callback(
            new Error(t("colonization.validation.enterSelectionThreshold"))
          );
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

// 创建响应式的表单验证规则
const formRules = ref(getFormRules());

const {
  colonization_database_select,
  colonization_sourcedata_getway,
  colonization_algorithm_type,
  colonization_envdata_getway,
  colonization_strategy,
  colonization_models,
  colonization_model_eval,
  colonization_algo,
  colonization_model_strategy,
} = proxy.useDict(
  "colonization_database_select",
  "colonization_sourcedata_getway",
  "colonization_algorithm_type",
  "colonization_envdata_getway",
  "colonization_strategy",
  "colonization_models",
  "colonization_model_eval",
  "colonization_algo",
  "colonization_model_strategy"
);

const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
});

const data = reactive({
  // 发生数据优化弹框
  optimizeParams: {
    open: false,
    title: computed(() => t("colonization.occurrenceDataOptimizationParams")),
    submitLoading: false,
    dataForm: {
      thinPar: null,
      reps: null,
    },
    rules: {
      thinPar: [
        {
          required: true,
          message: "请输入稀疏化距离参数",
          trigger: "blur",
        },
      ],
      reps: [
        {
          required: true,
          message: "请输入重复运行次数参数",
          trigger: "blur",
        },
      ],
    },
  },
  // 环境数据优化弹框
  envOptimizeParams: {
    open: false,
    title: computed(() => t("colonization.environmentDataOptimizationParams")),
    submitLoading: false,
    dataForm: {
      corThreshold: null,
    },
    rules: {
      corThreshold: [
        {
          required: true,
          message: "请输入相关系数",
          trigger: "blur",
        },
      ],
    },
  },
  queryParams: {
    dataForm: {
      // 获取发生数据相关字段
      getway: "1",
      database: "GBIF",
      speciesName: "",
      maxNumber: null,
      uploadFile: null,
      generationDataId: null, // 存储发生数据的ID，用于优化
      // 获取环境数据相关字段
      envGetway: "2",
      modernLocalFile: [],
      futureLocalFile: [],
      futureWeatherFiles: [],
      envDataId: null, // 存储环境数据的ID，用于优化
      // 建模分析相关字段
      algorithm: "",
      isFinetune: false,
      valFraction: null,
      pretrainFileId: null,
      predictFileId: null,
      finetuneFileId: null,

      // biomod 定殖适生算法相关字段
      ssName: "", // 物种名称
      rep: null, // 伪存在点重复次数
      absences: null, // 产生伪存在点数量
      strategy: "", // 伪存在点生成策略
      moduleDir: "", // 模型名称
      diskMin: null, // 最小距离
      diskMax: null, // 最大距离
      modelStrategy: "", // 模型验证方法
      models: [], // 模型算法
      modelRep: null, // 模型重复次数
      modelPerc: null, // 训练集比例
      modelK: null, // K折交叉验证
      modelEval: [], // 模型评估指标
      algo: [], // 集成方法
      metricSelect: [], // 模型筛选指标
      envMetric: [], // 环境数据指标
      metricSelectThresh0: null, // 第一个筛选阈值
      metricSelectThresh1: null, // 第二个筛选阈值
      metricSelectThresh2: null, // 第三个筛选阈值
    },
    fomRules: {
      // 获取发生数据验证规则
      getway: [
        { required: true, message: "请选择获取方式", trigger: "change" },
      ],
      database: [
        { required: true, message: "请选择数据库", trigger: "change" },
      ],
      speciesName: [
        { required: true, message: "请输入文件名称", trigger: "blur" },
      ],
      maxNumber: [
        { required: true, message: "请输入获取数据量", trigger: "blur" },
      ],
      uploadFile: [
        { required: true, message: "请上传发生数据文件", trigger: "change" },
      ],

      // 获取环境数据验证规则
      envGetway: [
        { required: true, message: "请选择获取方式", trigger: "change" },
      ],
      modernLocalFile: [
        {
          required: true,
          message: "请选择或上传现代气候数据",
          trigger: "change",
        },
      ],
      futureLocalFile: [
        {
          required: true,
          message: "请选择或上传未来气候数据",
          trigger: "change",
        },
      ],
      futureWeatherFiles: [
        { required: true, message: "请选择未来气候数据", trigger: "change" },
      ],

      // 建模分析验证规则
      algorithm: [
        { required: true, message: "请选择模型算法", trigger: "change" },
      ],
      isFinetune: [
        { required: true, message: "请选择是否微调", trigger: "change" },
      ],
      valFraction: [
        { required: true, message: "请输入训练集比例", trigger: "blur" },
      ],
      pretrainFileId: [
        { required: true, message: "请上传预训练文件", trigger: "change" },
      ],
      predictFileId: [
        { required: true, message: "请上传预测文件", trigger: "change" },
      ],
      finetuneFileId: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当选择微调时才验证微调文件
            if (queryParams.value.dataForm.isFinetune && !value) {
              callback(new Error("请上传微调文件"));
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],

      // biomod 定殖适生算法验证规则
      ssName: [{ required: true, message: "请输入物种名称", trigger: "blur" }],
      rep: [
        { required: true, message: "请输入伪存在点重复次数", trigger: "blur" },
      ],
      absences: [
        { required: true, message: "请输入产生伪存在点数量", trigger: "blur" },
      ],
      strategy: [
        {
          required: true,
          message: "请选择伪存在点生成策略",
          trigger: "change",
        },
      ],
      moduleDir: [
        { required: true, message: "请输入模型名称", trigger: "blur" },
      ],
      diskMin: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当策略为disk时才验证
            if (queryParams.value.dataForm.strategy === "disk" && !value) {
              callback(new Error("请输入最小距离"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      diskMax: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当策略为disk时才验证
            if (queryParams.value.dataForm.strategy === "disk" && !value) {
              callback(new Error("请输入最大距离"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      modelStrategy: [
        { required: true, message: "请选择模型验证方法", trigger: "change" },
      ],
      models: [
        { required: true, message: "请选择模型算法", trigger: "change" },
      ],
      modelRep: [
        { required: true, message: "请输入模型重复次数", trigger: "blur" },
      ],
      modelPerc: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当模型验证方法为random时才验证
            if (
              queryParams.value.dataForm.modelStrategy === "random" &&
              !value
            ) {
              callback(new Error("请输入训练集比例"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      modelK: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当模型验证方法不为random时才验证
            if (
              queryParams.value.dataForm.modelStrategy !== "random" &&
              !value
            ) {
              callback(new Error("请输入K折交叉验证"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      modelEval: [
        { required: true, message: "请选择模型评估指标", trigger: "change" },
      ],
      algo: [{ required: true, message: "请选择集成方法", trigger: "change" }],
      metricSelect: [
        { required: true, message: "请选择模型筛选指标", trigger: "change" },
      ],
      envMetric: [
        { required: true, message: "请选择环境数据指标", trigger: "change" },
      ],
      metricSelectThresh0: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当有第一个筛选指标时才验证
            if (
              queryParams.value.dataForm.metricSelect &&
              queryParams.value.dataForm.metricSelect.length > 0 &&
              !value
            ) {
              callback(new Error("请输入筛选阈值"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      metricSelectThresh1: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当有第二个筛选指标时才验证
            if (
              queryParams.value.dataForm.metricSelect &&
              queryParams.value.dataForm.metricSelect.length > 1 &&
              !value
            ) {
              callback(new Error("请输入筛选阈值"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      metricSelectThresh2: [
        {
          required: false,
          validator: (_, value, callback) => {
            // 只有当有第三个筛选指标时才验证
            if (
              queryParams.value.dataForm.metricSelect &&
              queryParams.value.dataForm.metricSelect.length > 2 &&
              !value
            ) {
              callback(new Error("请输入筛选阈值"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
    },
    btnLoading: false,
  },

  // 加载状态
  step1Loading: false,
  loading: false,

  // 上传数据配置
  uploadData: {
    isUploading: false,
    headers: { Authorization: "Bearer " + getToken() },
    url:
      import.meta.env.VITE_APP_BASE_API + `/system/generationData/data/upload`,
  },

  // 布局设置 展开折叠
  layoutSetting: {
    showBasic: true,
    showModelParam: true,
    showRoadWeight: true,
    showInputFile: true,
  },
  sourceList: [],
  updateMapKey: 0, // 更新地图
  runRusultData: {},
  updateTableKey: 0, // 更新表格
  pSimRange: {
    min: 0,
    max: 1,
  },

  // 各步骤的加载状态
  step1Loading: false,
  step2Loading: false,
  step3Loading: false,

  // 环境数据选项
  nowWeatherOptions: [],
  tomWeatherOptions: [],

  // 结果预览相关
  pictureList: [], // 图片列表，用于轮播图显示
  chartDataGraphKey3: 0, // 轮播图更新key
  resultTaskId: null, // 任务结果ID，用于下载

  // 控制结果预览显示内容
  showResultType: "tableList", // "tableList" | "carousel" | "none"

  // 环境数据优化结果弹框
  envResultDialog: {
    open: false,
    title: computed(() => t("colonization.environmentDataOptimizationResults")),
    data: null,
  },
  // 切换算法模型，更新上传组件
  updateUploadKey1: 0,
  updateUploadKey2: 0,
  updateUploadKey3: 0,
});

const {
  queryParams,
  optimizeParams,
  envOptimizeParams,
  layoutSetting,
  sourceList,
  pSimRange,
  step1Loading,
  step2Loading,
  step3Loading,
  uploadData,
  nowWeatherOptions,
  tomWeatherOptions,
  pictureList,
  chartDataGraphKey3,
  resultTaskId,
  showResultType,
  envResultDialog,
  updateUploadKey1,
  updateUploadKey2,
  updateUploadKey3,
  updateMapKey,
} = toRefs(data);

// 计算属性：动态生成现代气候数据上传URL
const modernUploadUrl = computed(() => {
  const baseUrl = "/system/envData/upload/modern";
  const modernFile = queryParams.value.dataForm?.modernLocalFile;

  // 如果 modernLocalFile 是数组且有内容，取第一个元素作为ID
  if (Array.isArray(modernFile) && modernFile?.length > 0) {
    return `${baseUrl}?id=${modernFile[0]}`;
  }
  // 如果 modernLocalFile 是字符串且不为空
  else if (typeof modernFile === "string" && modernFile?.length > 0) {
    return `${baseUrl}?id=${modernFile}`;
  }

  return baseUrl;
});

// 计算属性：动态生成未来气候数据上传URL
const futureUploadUrl = computed(() => {
  const baseUrl = "/system/envData/upload/future";
  const futureFile = queryParams.value.dataForm?.futureLocalFile;

  // 如果 futureLocalFile 是数组且有内容，取第一个元素作为ID
  if (Array.isArray(futureFile) && futureFile?.length > 0) {
    return `${baseUrl}?id=${futureFile[0]}`;
  }
  // 如果 futureLocalFile 是字符串且不为空
  else if (typeof futureFile === "string" && futureFile.length > 0) {
    return `${baseUrl}?id=${futureFile}`;
  }

  return baseUrl;
});

// 重置所有步骤
const handleReset = () => {
  queryParams.value.dataForm = {
    // 获取发生数据相关字段
    getway: "1",
    database: "GBIF",
    speciesName: "",
    maxNumber: null,
    uploadFile: null,
    generationDataId: null,
    // 获取环境数据相关字段
    envGetway: "2",
    modernLocalFile: [],
    futureLocalFile: [],
    futureWeatherFiles: [],
    envDataId: null,
    // 建模分析相关字段
    algorithm: "",
    isFinetune: false,
    valFraction: null,
    pretrainFileId: null,
    predictFileId: null,
    finetuneFileId: null,
    // biomod 定殖适生算法相关字段
    ssName: "",
    rep: null,
    absences: null,
    strategy: "",
    moduleDir: "",
    diskMin: null,
    diskMax: null,
    modelStrategy: "",
    models: [],
    modelRep: null,
    modelPerc: null,
    modelK: null,
    modelEval: [],
    algo: [],
    metricSelect: [],
    envMetric: [],
    metricSelectThresh0: null,
    metricSelectThresh1: null,
    metricSelectThresh2: null,
  };
};

// Step 1 重置
const handleStep1Reset = () => {
  if (queryParams.value.dataForm.getway == 2) {
    return;
  }
  queryParams.value.dataForm.getway = "1";
  queryParams.value.dataForm.database = "GBIF";
  queryParams.value.dataForm.speciesName = "";
  queryParams.value.dataForm.maxNumber = null;
  queryParams.value.dataForm.uploadFile = null;
};

// Step 2 重置
const handleStep2Reset = () => {
  queryParams.value.dataForm.envGetway = "1";
  queryParams.value.dataForm.modernLocalFile = [];
  queryParams.value.dataForm.futureLocalFile = [];
  queryParams.value.dataForm.futureWeatherFiles = [];
  queryParams.value.dataForm.envDataId = null; // 清除环境数据ID
};

// Step 3 重置
const handleStep3Reset = () => {
  queryParams.value.dataForm.algorithm = "";
  queryParams.value.dataForm.isFinetune = false;
  queryParams.value.dataForm.valFraction = null;
  queryParams.value.dataForm.pretrainFileId = null;
  queryParams.value.dataForm.predictFileId = null;
  queryParams.value.dataForm.finetuneFileId = null;

  // 重置biomod相关字段
  queryParams.value.dataForm.ssName = "";
  queryParams.value.dataForm.rep = null;
  queryParams.value.dataForm.absences = null;
  queryParams.value.dataForm.strategy = "";
  queryParams.value.dataForm.moduleDir = "";
  queryParams.value.dataForm.diskMin = null;
  queryParams.value.dataForm.diskMax = null;
  queryParams.value.dataForm.modelStrategy = "";
  queryParams.value.dataForm.models = [];
  queryParams.value.dataForm.modelRep = null;
  queryParams.value.dataForm.modelPerc = null;
  queryParams.value.dataForm.modelK = null;
  queryParams.value.dataForm.modelEval = [];
  queryParams.value.dataForm.algo = [];
  queryParams.value.dataForm.metricSelect = [];
  queryParams.value.dataForm.envMetric = [];
  queryParams.value.dataForm.metricSelectThresh0 = null;
  queryParams.value.dataForm.metricSelectThresh1 = null;
  queryParams.value.dataForm.metricSelectThresh2 = null;
};

// 第一步获取方式切换处理
const handleStep1GetwayChange = () => {
  // 清空相关字段
  queryParams.value.dataForm.speciesName = "";
  queryParams.value.dataForm.maxNumber = null;
  queryParams.value.dataForm.uploadFile = null;
  queryParams.value.dataForm.generationDataId = null;
};

// 第二步获取方式切换处理
const handleStep2GetwayChange = () => {
  // 清空相关字段
  queryParams.value.dataForm.modernLocalFile = [];
  queryParams.value.dataForm.futureLocalFile = [];
  queryParams.value.dataForm.futureWeatherFiles = [];
  queryParams.value.dataForm.envDataId = null;
};

// 模型算法切换处理
const handleAlgorithmChange = (value) => {
  // 清空附件和表单字段
  queryParams.value.dataForm.isFinetune = false;
  queryParams.value.dataForm.valFraction = null;
  queryParams.value.dataForm.pretrainFileId = null;
  queryParams.value.dataForm.predictFileId = null;
  queryParams.value.dataForm.finetuneFileId = null;

  // 清空biomod相关字段
  queryParams.value.dataForm.ssName = "";
  queryParams.value.dataForm.rep = null;
  queryParams.value.dataForm.absences = null;
  queryParams.value.dataForm.strategy = "";
  queryParams.value.dataForm.moduleDir = "";
  queryParams.value.dataForm.diskMin = null;
  queryParams.value.dataForm.diskMax = null;
  queryParams.value.dataForm.modelStrategy = "";
  queryParams.value.dataForm.models = [];
  queryParams.value.dataForm.modelRep = null;
  queryParams.value.dataForm.modelPerc = null;
  queryParams.value.dataForm.modelK = null;
  queryParams.value.dataForm.modelEval = [];
  queryParams.value.dataForm.algo = [];
  queryParams.value.dataForm.metricSelect = [];
  queryParams.value.dataForm.envMetric = [];
  queryParams.value.dataForm.metricSelectThresh0 = null;
  queryParams.value.dataForm.metricSelectThresh1 = null;
  queryParams.value.dataForm.metricSelectThresh2 = null;

  updateUploadKey1.value++;
  updateUploadKey2.value++;
  updateUploadKey3.value++;

  // 根据算法类型检查任务状态
  if (value === "regressor") {
    showResultType.value = "carousel";
    // 回归任务 - 检查是否有正在执行的回归任务
    regressorTaskStatus()
      .then((response) => {
        if (response.data && response.data.hasRunningTask) {
          proxy.$modal.msgWarning(
            "当前有正在执行的回归任务，请等待任务完成后再操作"
          );
        }
      })
      .catch((error) => {
        console.error("检查回归任务状态失败:", error);
      });
  } else if (value === "classifier") {
    showResultType.value = "carousel";
    // 分类任务 - 检查是否有正在执行的分类任务
    classifierTaskStatus()
      .then((response) => {
        if (response.data && response.data.hasRunningTask) {
          proxy.$modal.msgWarning(
            "当前有正在执行的分类任务，请等待任务完成后再操作"
          );
        }
      })
      .catch((error) => {
        console.error("检查分类任务状态失败:", error);
      });
  } else if (value === "biomod") {
    showResultType.value = "tableList";
    // 定殖适生算法 - 检查是否有正在执行的biomod任务
    biomodelingTaskStatus()
      .then((response) => {
        if (response.data && response.data.hasRunningTask) {
          proxy.$modal.msgWarning(
            "当前有正在执行的定殖适生模型任务，请等待任务完成后再操作"
          );
        }
      })
      .catch((error) => {
        console.error("检查定殖适生模型任务状态失败:", error);
      });
  }
};
// Step 1 同步方法
const handleStep1Sync = () => {
  if (queryParams.value.dataForm.getway == 2) {
    return;
  }

  // 使用表单校验
  proxy.$refs["step1FormRef"].validate((valid) => {
    if (valid) {
      executeStep1Sync();
    } else {
      proxy.$modal.msgError("请完善必填信息");
      return false;
    }
  });
};

// 执行第一步数据获取
const executeStep1Sync = () => {
  step1Loading.value = true;
  const params = {
    speciesName: queryParams.value.dataForm.speciesName,
    maxNumber: queryParams.value.dataForm.maxNumber,
  };
  queryGenerationData(params)
    .then((res) => {
      console.log(res);
      step1Loading.value = false;

      // 保存返回的ID用于优化和传给tableList
      if (res.data && res.data.id) {
        queryParams.value.dataForm.generationDataId = res.data.id;
      }

      // 处理返回的generationDataRemoteVOList，传给spreadMap
      if (res.data && res.data.generationDataRemoteVOList) {
        sourceList.value = res.data.generationDataRemoteVOList;
        updateMapKey.value++; // 更新地图
      }

      // 第一步同步成功后显示 tableList
      showResultType.value = "tableList";
      proxy.$modal.msgSuccess(`获取发生数据同步成功`);
    })
    .catch(() => {
      step1Loading.value = false;
    });
};

// Step 1 优化方法 - 打开优化参数弹框
const handleStep1Optimize = () => {
  if (!queryParams.value.dataForm.generationDataId) {
    proxy.$modal.msgError("请先完成数据获取或文件上传");
    return;
  }

  // 重置表单数据
  optimizeParams.value.dataForm = {
    thinPar: null,
    reps: null,
  };

  // 打开弹框
  optimizeParams.value.open = true;
};

// 提交优化参数
const submitOptimizeParams = () => {
  proxy.$refs["optimizeParamsRef"].validate((valid) => {
    if (valid) {
      optimizeParams.value.submitLoading = true;

      // 使用 FormData 格式
      const formData = new FormData();
      formData.append(
        "generateDataId",
        queryParams.value.dataForm.generationDataId
      );
      formData.append("thinPar", optimizeParams.value.dataForm.thinPar);
      formData.append("reps", optimizeParams.value.dataForm.reps);

      generationDataOptimize(formData)
        .then((response) => {
          optimizeParams.value.submitLoading = false;
          optimizeParams.value.open = false;
          // 第一步优化成功后显示 tableList
          showResultType.value = "tableList";
          proxy.$modal.msgSuccess("发生数据优化成功");
          // 可以在这里处理优化结果
          console.log("优化结果:", response.data);
        })
        .catch((error) => {
          optimizeParams.value.submitLoading = false;
          proxy.$modal.msgError("发生数据优化失败");
          console.error("优化失败:", error);
        });
    }
  });
};

// 取消优化参数弹框
const cancelOptimizeParams = () => {
  optimizeParams.value.open = false;
};

// Step 2 同步方法
const handleStep2Sync = () => {
  if (queryParams.value.dataForm.envGetway == 2) {
    return;
  }

  // 使用表单校验
  proxy.$refs["step2FormRef"].validate((valid) => {
    if (valid) {
      executeStep2Sync();
    } else {
      proxy.$modal.msgError("请完善必填信息");
      return false;
    }
  });
};

// 执行第二步环境数据获取
const executeStep2Sync = () => {
  step2Loading.value = true;

  // 确保 modernLocalFile 和 futureWeatherFiles 是一维数组
  const modernLocalFile = queryParams.value.dataForm.modernLocalFile;
  const futureWeatherFiles = queryParams.value.dataForm.futureWeatherFiles;

  // 处理 modernLocalFile，确保是一维数组
  let processedModernLocalFile = [];
  if (Array.isArray(modernLocalFile)) {
    processedModernLocalFile = modernLocalFile.flat(); // 扁平化数组
  } else if (modernLocalFile) {
    processedModernLocalFile = [modernLocalFile];
  }

  // 处理 futureWeatherFiles，确保是一维数组
  let processedFutureWeatherFiles = [];
  if (Array.isArray(futureWeatherFiles)) {
    processedFutureWeatherFiles = futureWeatherFiles.flat(); // 扁平化数组
  } else if (futureWeatherFiles) {
    processedFutureWeatherFiles = [futureWeatherFiles];
  }

  const params = {
    modernLocalFile: processedModernLocalFile,
    futureWeatherFiles: processedFutureWeatherFiles,
  };

  console.log("环境数据同步参数:", params);

  envDataLocal(params)
    .then((res) => {
      step2Loading.value = false;
      // 保存返回的ID用于优化
      if (res.data && res.data.id) {
        queryParams.value.dataForm.envDataId = res.data.id;
      }
      proxy.$modal.msgSuccess("环境数据同步成功");
    })
    .catch((error) => {
      step2Loading.value = false; // 确保在错误时取消loading状态
      console.error("环境数据同步失败:", error);
      proxy.$modal.msgError("环境数据同步失败，请重试");
    });
};

// Step 2 环境数据优化 - 打开优化参数弹框
const handleStep2Optimize = () => {
  if (!queryParams.value.dataForm.generationDataId) {
    proxy.$modal.msgError("请先同步发生数据");
    return;
  }

  if (!queryParams.value?.dataForm?.modernLocalFile?.length) {
    console.log(queryParams.value?.dataForm?.modernLocalFile);
    proxy.$modal.msgError("请先完成环境数据获取");
    return;
  }

  // 重置表单数据
  envOptimizeParams.value.dataForm = {
    corThreshold: 0.7,
  };

  // 打开弹框
  envOptimizeParams.value.open = true;
};

// 提交环境数据优化参数
const submitEnvOptimizeParams = () => {
  proxy.$refs["envOptimizeParamsRef"].validate((valid) => {
    if (valid) {
      envOptimizeParams.value.submitLoading = true;

      // 使用 FormData 格式
      const formData = new FormData();
      formData.append(
        "generationDataId",
        queryParams.value.dataForm.generationDataId
      );
      formData.append("envDataId", queryParams.value.dataForm.modernLocalFile);
      formData.append(
        "corThreshold",
        envOptimizeParams.value.dataForm.corThreshold
      );

      envDataOptimize(formData)
        .then((response) => {
          envOptimizeParams.value.submitLoading = false;
          envOptimizeParams.value.open = false;
          proxy.$modal.msgSuccess("环境数据优化成功");
          console.log("环境数据优化结果:", response.data);
        })
        .catch((error) => {
          envOptimizeParams.value.submitLoading = false;

          console.error("环境数据优化失败:", error);
        });
    }
  });
};

// 取消环境数据优化参数弹框
const cancelEnvOptimizeParams = () => {
  envOptimizeParams.value.open = false;
};

// 关闭环境数据优化结果弹框
const closeEnvResultDialog = () => {
  envResultDialog.value.open = false;
};

// Step 2 查看环境数据优化结果
const handleStep2ViewResult = () => {
  step2Loading.value = true;
  const params = {
    envDataId:
      queryParams.value.dataForm.envDataId ||
      queryParams.value?.dataForm?.modernLocalFile,
  };
  envResultDialog.value.data = null;
  envDataOptimizeResult(params)
    .then((response) => {
      step2Loading.value = false;
      if (response.code === 200 && response.data) {
        // 设置弹框数据并打开弹框
        envResultDialog.value.data = response.data;
        envResultDialog.value.open = true;
        // proxy.$modal.msgSuccess("获取优化结果成功");
        console.log("环境数据优化结果:", response.data);
      } else {
        proxy.$modal.msgError(response.msg || "获取优化结果失败");
      }
    })
    .catch((error) => {
      step2Loading.value = false;
      // proxy.$modal.msgError("获取优化结果失败");
      console.error("获取优化结果失败:", error);
    });
};

// Step 3 建模分析算法调用
const handleStep3Sync = () => {
  // 使用表单校验
  proxy.$refs["step3FormRef"].validate((valid) => {
    if (valid) {
      executeStep3Algorithm();
    } else {
      proxy.$modal.msgError("请完善必填信息");
      return false;
    }
  });
};

// 执行第三步算法调用
const executeStep3Algorithm = () => {
  step3Loading.value = true;

  // 根据算法类型选择不同的接口
  const algorithm = queryParams.value.dataForm.algorithm;
  const baseParams = {
    // generationDataId: queryParams.value.dataForm.generationDataId,
    // envDataId: queryParams.value.dataForm.envDataId,
    algorithm: algorithm,
    isFinetune: queryParams.value.dataForm.isFinetune,
    valFraction: queryParams.value.dataForm.valFraction,
    pretrainFileId: queryParams.value.dataForm.pretrainFileId,
    predictFileId: queryParams.value.dataForm.predictFileId,
    finetuneFileId: queryParams.value.dataForm.finetuneFileId,
  };

  // 根据算法类型调用不同接口
  if (algorithm === "regressor") {
    // 回归任务
    regressionTaskExecute(baseParams)
      .then((response) => {
        step3Loading.value = false;
        // 第三步算法调用成功后显示 carousel
        showResultType.value = "carousel";
        proxy.$modal.msgSuccess(response.msg);
        console.log("回归任务结果:", response.data);

        // 算法调用成功后，延迟一段时间自动刷新结果预览
        setTimeout(() => {
          handelRefresh();
        }, 2000);
      })
      .catch((error) => {
        step3Loading.value = false;
        // proxy.$modal.msgError("回归任务执行失败");
        console.error("回归任务执行失败:", error);
      });
  } else if (algorithm === "classifier") {
    // 分类任务
    classifierList(baseParams)
      .then((response) => {
        step3Loading.value = false;
        // 第三步算法调用成功后显示 carousel
        showResultType.value = "carousel";
        proxy.$modal.msgSuccess(response.msg);
        console.log("分类任务结果:", response.data);

        // 算法调用成功后，延迟一段时间自动刷新结果预览
        setTimeout(() => {
          handelRefresh();
        }, 2000);
      })
      .catch((error) => {
        step3Loading.value = false;
        // proxy.$modal.msgError("分类任务执行失败");
        console.error("分类任务执行失败:", error);
      });
  } else if (algorithm === "biomod") {
    // 定殖适生算法 (biomod)
    const biomodParams = {
      // 前两步的ID参数
      generationDataId: queryParams.value.dataForm.generationDataId,
      envDataId:
        typeof queryParams.value.dataForm.modernLocalFile == "string"
          ? queryParams.value.dataForm.modernLocalFile
          : typeof queryParams.value.dataForm.futureLocalFile === "string"
          ? queryParams.value.dataForm.futureLocalFile
          : "",

      // biomod表单参数
      ssName: queryParams.value.dataForm.ssName,
      rep: queryParams.value.dataForm.rep,
      absences: queryParams.value.dataForm.absences,
      strategy: queryParams.value.dataForm.strategy,
      moduleDir: queryParams.value.dataForm.moduleDir,
      modelStrategy: queryParams.value.dataForm.modelStrategy,
      models: queryParams.value.dataForm.models,
      modelRep: queryParams.value.dataForm.modelRep,
      modelEval: queryParams.value.dataForm.modelEval,
      algo: queryParams.value.dataForm.algo,
      metricSelect: queryParams.value.dataForm.metricSelect,
      envMetric: queryParams.value.dataForm.envMetric,
    };

    // 添加条件字段
    if (queryParams.value.dataForm.strategy === "disk") {
      biomodParams["diskMin"] = queryParams.value.dataForm.diskMin;
      biomodParams["diskMax"] = queryParams.value.dataForm.diskMax;
    }

    if (queryParams.value.dataForm.modelStrategy === "random") {
      biomodParams["modelPerc"] = queryParams.value.dataForm.modelPerc;
    } else {
      biomodParams["modelK"] = queryParams.value.dataForm.modelK;
    }

    // 组合 metricSelectThres 数组
    const metricSelectThresh = [];
    if (
      queryParams.value.dataForm.metricSelectThresh0 !== null &&
      queryParams.value.dataForm.metricSelectThresh0 !== undefined
    ) {
      metricSelectThresh.push(queryParams.value.dataForm.metricSelectThresh0);
    }
    if (
      queryParams.value.dataForm.metricSelectThresh1 !== null &&
      queryParams.value.dataForm.metricSelectThresh1 !== undefined
    ) {
      metricSelectThresh.push(queryParams.value.dataForm.metricSelectThresh1);
    }
    if (
      queryParams.value.dataForm.metricSelectThresh2 !== null &&
      queryParams.value.dataForm.metricSelectThresh2 !== undefined
    ) {
      metricSelectThresh.push(queryParams.value.dataForm.metricSelectThresh2);
    }

    if (metricSelectThresh.length > 0) {
      biomodParams["metricSelectThresh"] = metricSelectThresh;
    }

    console.log("biomod参数:", biomodParams);

    biomodeling(biomodParams)
      .then((response) => {
        step3Loading.value = false;
        // 第三步算法调用成功后显示 carousel
        showResultType.value = "tableList";
        proxy.$modal.msgSuccess(response.msg);
        console.log("biomod任务结果:", response.data);
      })
      .catch((error) => {
        step3Loading.value = false;
        console.error("biomod任务执行失败:", error);
      });
  } else {
    step3Loading.value = false;
    proxy.$modal.msgError("不支持的算法类型");
  }
};

// Step 1 文件上传处理
const handleFileUploadProgress = () => {
  uploadData.value.isUploading = true;
};

const handleFileSuccess = (response, file) => {
  uploadData.value.isUploading = false;
  file.data = response.data;
  // 保存上传文件返回的ID用于优化
  if (response.data && response.data.id) {
    // 第一步同步成功后显示 tableList
    showResultType.value = "tableList";
    queryParams.value.dataForm.generationDataId = response.data.id;

    if (response.data.generationDataRemoteVOList) {
      sourceList.value = response.data.generationDataRemoteVOList;
      updateMapKey.value++; // 更新地图
    }
  }
  proxy.$modal.msgSuccess("文件上传成功");
};

const handleFileRemove = (file) => {
  uploadData.value.isUploading = true;
  const params = {
    id: file.data.id,
    fileId: file.data?.uploadFile[0]?.id,
  };
  removeGenerationFile(params).then(() => {
    uploadData.value.isUploading = false;
    // 清除保存的ID
    queryParams.value.dataForm.generationDataId = null;
    proxy.$modal.msgSuccess("删除成功");
  });
};

const handleFileExceed = () => {
  proxy.$modal.msgWarning("请先删除已上传的文件");
};

// 获取环境文件树
const getEnvFileTree = () => {
  Promise.all([
    queryEnvFileTree({ envFileType: "MODERN" }),
    queryEnvFileTree({ envFileType: "FUTURE" }),
  ]).then((res) => {
    nowWeatherOptions.value = res[0].data;
    tomWeatherOptions.value = res[1].data;
  });
};

// 结果预览相关函数

// 刷新结果预览
const handelRefresh = async () => {
  try {
    // 根据算法类型调用不同的结果接口
    const algorithm = queryParams.value.dataForm.algorithm;
    if (!algorithm) {
      proxy.$modal.msgWarning("请先选择算法类型");
      return;
    }

    proxy.$modal.loading("正在获取结果数据...");

    // 构建请求参数 - 根据实际API要求调整
    const params = {
      type: algorithm,
    };

    // 调用获取回归任务结果接口
    const response = await regressorTaskResult(params);

    if (response.code === 200 && response.data) {
      // 处理返回的图片数据
      if (
        response?.data?.result &&
        Array.isArray(response?.data?.result) &&
        response?.data?.result?.length
      ) {
        pictureList.value = response?.data?.result?.map(
          (item) =>
            `${window.location.protocol}//${window.location.host}/file/${item.url}`
        );
      } else {
        pictureList.value = [];
      }

      // 保存任务ID用于下载
      if (response.data.id) {
        resultTaskId.value = response.data.id;
      }

      // 更新轮播图
      chartDataGraphKey3.value++;

      // 第三步算法调用成功后显示 carousel
      showResultType.value = "carousel";

      proxy.$modal.msgSuccess("结果数据获取成功");
    } else {
      proxy.$modal.msgError(response.msg || "获取结果数据失败");
      pictureList.value = [];
    }
  } catch (error) {
    console.error("获取结果数据失败:", error);
    pictureList.value = [];
  } finally {
    proxy.$modal.closeLoading();
  }
};

// 下载结果
const handleDownload = async () => {
  try {
    if (!resultTaskId.value) {
      proxy.$modal.msgWarning("暂无可下载的结果数据，请先刷新获取结果");
      return;
    }

    proxy.download(
      "/organism/algorithmInfo/batchDownload",
      {
        id: resultTaskId.value,
      },
      ``,
      { timeout: 60000 }
    );
  } catch (error) {
    console.error("下载失败:", error);
    proxy.$modal.msgError("下载失败");
  } finally {
    proxy.$modal.closeLoading();
  }
};

function handeldownloadTemplate(
  type,
  templateUrl = "organism/algorithmInfo/template/download"
) {
  const templateName = {
    1: "预训练文件模板",
    2: "微调文件模板",
    3: "预测文件模板",
    4: "未来环境数据",
    5: "发生数据需优化数据",
    6: "发生数据最终数据模板",
  }[type || "1"];
  proxy.download(
    templateUrl,
    {
      type: type,
    },
    `${templateName}.csv`
  );
}

onMounted(() => {
  handleReset();
  // getTaskStatus();
  getEnvFileTree();
});
</script>

<style scoped lang="scss">
:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

:deep(.el-form-item--large .el-form-item__label) {
  font-size: 14px;
  font-weight: normal;
  // line-height: 24px;
}

:deep(.el-form-item__label) {
  line-height: 36px; /* 正常行高 */
  white-space: normal; /* 允许换行 */
  word-break: break-all;
}

/* 环境数据优化结果弹框样式 */
.result-content {
  max-height: 500px;
  overflow-y: auto;
}

.result-card {
  margin-bottom: 0;
}

.json-display {
  border-radius: 4px;
  padding: 16px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

:deep(.v-line-24 .el-form-item__label) {
  line-height: 22px;
}

.drag-upload {
  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 216px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    // padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 200px;
      padding-top: 20px;

      .el-icon--upload {
        margin-top: 0px;
        margin-bottom: 10px;
      }

      .el-icon {
        height: 1em;
        width: 1em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 12px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }
}

.form-footer {
  margin-top: 30px;
  text-align: center;
}
</style>

<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form
          size="large"
          :model="queryParams"
          ref="queryRef"
          :inline="false"
        >
          <el-form-item label="" prop="testKey">
            <el-upload
              ref="uploadRef"
              :limit="1"
              class="upload-file"
              name="uploadFile"
              :data="{
                type: stype,
              }"
              accept=".xlsx, .xls"
              limit="1"
              :headers="uploadData.headers"
              :action="uploadData.url"
              auto-upload
              :on-progress="handleFileUploadProgress"
              :on-success="handleFileSuccess"
              :on-exceed="handleFileExceed"
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                {{ t("risk.dragFileHere")
                }}<em>{{ t("risk.clickToUpload") }}</em>

                <el-link
                  class="v-font-14"
                  :underline="false"
                  style="position: relative; top: -1px; left: 5px"
                  type="primary"
                  @click.stop="handeldownloadTemplate"
                  >下载模板</el-link
                >
              </div>
            </el-upload>
          </el-form-item>

          <el-form-item
            class="v-m-t-70 v-m-b-40"
            v-if="stype == 1"
            label=""
            prop="runWay"
          >
            <el-switch
              v-model="queryParams.runWay"
              class="mb-2"
              size="large"
              :active-text="t('risk.runDirectedNetwork')"
              :inactive-text="t('risk.runUndirectedNetwork')"
            />
          </el-form-item>
          <el-form-item
            class="v-m-t-60"
            v-if="stype == 2"
            :label="t('risk.clusteringMethod')"
            prop="methods"
          >
            <el-select
              v-model="queryParams.methods"
              :placeholder="t('risk.clusteringMethodPlaceholder')"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              style="width: 100%"
            >
              <el-option
                v-for="dict in analysis_cluster_method"
                :key="dict.value"
                :label="getDictLabel(dict)"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="stype == 3"
            :label="t('risk.riskAssessmentIndicators')"
            prop="metrics"
          >
            <el-select
              v-model="queryParams.metrics"
              :placeholder="t('risk.riskAssessmentIndicatorsPlaceholder')"
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              style="width: 100%"
            >
              <el-option
                v-for="dict in evaluate_indicator_method"
                :key="dict.value"
                :label="getDictLabel(dict)"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="stype == 3"
            :label="t('risk.removalQuantityPerTime')"
            prop="batchSize"
          >
            <el-input-number
              v-model="queryParams.batchSize"
              :min="1"
              :max="100000"
              :precision="0"
              style="width: 100%"
              :placeholder="t('risk.removalQuantityPerTimePlaceholder')"
              :controls="false"
            />
          </el-form-item>

          <el-form-item
            v-if="stype == 3"
            :label="t('risk.selectInterfaceLanguage')"
            prop="language"
          >
            <el-radio-group v-model="queryParams.language">
              <el-radio label="Chinese">{{ t("risk.chinese") }}</el-radio>
              <el-radio label="English">{{ t("risk.english") }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <div class="v-flex v-row-center" style="width: 100%">
              <el-button
                :loading="echartsLoading"
                v-if="stype == 1"
                type="success"
                class="v-m-x-20"
                icon="histogram"
                :disabled="!queryParams.fileId"
                @click="handleAnalysis"
                >{{ t("risk.analysisRun") }}</el-button
              >
              <el-button
                type="success"
                class="v-m-x-0"
                v-if="stype == 1"
                :disabled="!queryParams.fileId"
                :loading="downlLoadLoading"
                icon="pointer"
                @click="handleExport"
                >{{ t("risk.downloadReport") }}</el-button
              >
              <el-button
                class="v-m-x-40 v-m-t-24"
                type="success"
                v-if="stype == 2"
                :disabled="!queryParams.fileId || !queryParams.methods.length"
                :loading="echartsLoading"
                icon="pointer"
                @click="handleClass"
                >{{ t("risk.startClustering") }}</el-button
              >

              <el-button
                :loading="echartsLoading"
                v-if="stype == 3"
                type="success"
                icon="histogram"
                :disabled="
                  !queryParams.fileId ||
                  !(queryParams.metrics.length > 1) ||
                  !queryParams.batchSize ||
                  !queryParams.language
                "
                @click="handleAnalysis2"
                >{{ t("risk.startAnalysis") }}</el-button
              >
              <el-button
                class="v-m-x-40"
                type="success"
                v-if="stype == 3"
                :disabled="
                  !queryParams.fileId ||
                  !chartDataGraphSource?.pictureList?.length
                "
                icon="download"
                :loading="downlLoadLoading"
                @click="handleExport"
                >{{ t("risk.exportResults") }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="16">
        <el-row class="v-m-t-10">
          <el-card
            shadow="hover"
            style="width: 100%"
            :class="{
              'container-scale-big-close':
                ['1', '2'].includes(stype) &&
                scaleObject.scaleBigShow === false,
              'container-scale-big':
                ['1', '2'].includes(stype) && scaleObject.scaleBigShow,
            }"
            :key="scaleObject.key"
          >
            <template #header>
              <div class="v-flex v-row-between">
                <div class="v-title">{{ titleCardChart[stype] }}</div>
                <div
                  @click="handelScale"
                  class="v-pointer v-link"
                  v-if="['4'].includes(stype)"
                >
                  <el-tooltip
                    class="box-item"
                    effect="light"
                    :content="
                      t('risk.clickToScale', {
                        action: scaleObject.scaleBigShow
                          ? t('risk.restore')
                          : t('risk.enlarge'),
                      })
                    "
                    placement="top-start"
                  >
                    <el-icon v-if="!scaleObject.scaleBigShow"
                      ><ZoomIn
                    /></el-icon>
                    <el-icon v-else><ZoomOut /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
            <div
              class="chart-box"
              :key="scaleObject.key"
              :loading="echartsLoading"
              :style="{ height: { 1: '466px', 2: '461px', 3: '522px' }[stype] }"
            >
              <GraphCharts
                chartType="visualMap"
                @updateData="handleUpdateData"
                :key="chartDataGraphKey"
                :data="chartDataGraph"
                :index="chartDataGraphMethodIndex"
                :methods="chartDataGraphSource.method"
                v-if="stype == 1 && chartDataGraph.nodes.length"
              />
              <GraphCharts
                chartType="legend"
                :data="chartDataGraph"
                :index="chartDataGraphMethodIndex"
                :methods="chartDataGraphSource.method"
                @updateData="handleUpdateData"
                :key="chartDataGraphKey2"
                v-else-if="stype == 2 && chartDataGraph.nodes.length"
              />
              <el-carousel
                v-else-if="
                  stype == 3 && chartDataGraphSource?.pictureList?.length
                "
                :interval="4000"
                :autoplay="false"
                :loop="false"
                :motion-blur="true"
                height="510px"
                :key="chartDataGraphKey3"
              >
                <el-carousel-item
                  v-for="item in chartDataGraphSource?.pictureList"
                  :key="item"
                  class="v-pointer"
                >
                  <el-image
                    ref="imageRef"
                    :preview-teleported="true"
                    style="width: 100%; height: 100%"
                    :src="chartDataGraphSource?.pictureList[0]"
                    :preview-src-list="chartDataGraphSource?.pictureList"
                    fit="contain"
                  />
                </el-carousel-item>
              </el-carousel>
              <el-empty
                v-else="false"
                :description="t('risk.noAnalysisData')"
              />
            </div>
          </el-card>
        </el-row>
      </el-col>
    </el-row>

    <el-row>
      <el-card shadow="hover" style="width: 100%">
        <template #header>
          <div class="v-flex v-row-between">
            <div class="v-title">{{ t("risk.resultPreview") }}</div>
            <div class="v-pointer v-link" @click="handlePreview">
              <el-tooltip
                class="box-item"
                effect="light"
                :content="t('risk.openPreviewInNewPage')"
                placement="top-start"
              >
                <el-icon><Share /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </template>
        <div class="result-box">
          <iframe
            :src="`${chartDataGraphSource.previewUrl}`"
            width="100%"
            height="400px"
            scrolling="no"
            frameborder="0"
            v-if="chartDataGraphSource.previewUrl"
          ></iframe>
          <el-empty v-else :description="t('risk.noAnalysisData')" />
        </div>
      </el-card>
    </el-row>
  </div>
</template>

<script setup>
import {
  analysisInfoCentral,
  analysisInfoCluster,
  analysisInfoWeakness, // 脆弱性分析
  analysisInfoPreviewResult, // 分析结果预览链接
  downloadReport,
} from "@/api/risk";
import { getToken } from "@/utils/auth";
import GraphCharts from "@/components/GraphCharts/index";
import { saveAs } from "file-saver";
import { t } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from "@/composables/useLanguageChange";
import { blobValidate } from "@/utils/ruoyi";
const { proxy } = getCurrentInstance();

// 语言相关
const languageStore = useLanguageStore();
const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

const { analysis_cluster_method, evaluate_indicator_method } = proxy.useDict(
  "analysis_cluster_method",
  "evaluate_indicator_method"
);

// 计算属性：根据语言返回字典项的显示文本
const getDictLabel = (dict) => {
  return currentLanguage.value === "en" ? dict.value : dict.label;
};

const route = useRoute();
defineComponent({
  name: `${{ 1: "Center", 2: "Cluster", 3: "Weak" }[route.query.stype || "1"]}`,
});

const data = reactive({
  queryParams: {
    runWay: undefined, // 运行方式
    methods: undefined, // 聚类方法
    metrics: undefined, // 评估指标
    language: undefined, // 语言
  },
  uploadData: {
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API +
      `/organism/analysisInfo/uploadAnalysisFile`,
  },
  echartsLoading: false,
  resultLoading: false,
  downlLoadLoading: false,
  stype: route.query.stype || "1",
  titleCardChart: computed(() => ({
    1: t("risk.riskNodeCentralityAnalysis"),
    2: t("risk.clusteringResults"),
    3: t("risk.analysisResults"),
  })),
  echartsKey: 0, // 用于刷新echarts

  // 放大功能
  scaleObject: {
    scaleBigShow: false,
    key: 0,
  },
  chartDataGraph: {
    nodes: [],
    links: [],
  }, // 风险节点中心性分析数据
  chartDataGraphKey: 0, // 用于刷新echarts
  chartDataGraphKey2: 0, // 用于刷新echarts
  chartDataGraphKey3: 0, // 用于刷新echarts
  chartDataGraphSource: {}, // 风险节点中心性分析数据源
  chartDataGraphMethodIndex: 0, // 风险节点中心性分析方法索引
});

const {
  queryParams,
  uploadData,
  echartsLoading,
  resultLoading,
  downlLoadLoading,
  stype,
  titleCardChart,
  echartsKey,
  scaleObject,
  chartDataGraph, // 风险节点中心性分析数据
  chartDataGraphSource, // 风险节点中心性分析数据源
  chartDataGraphKey, // 用于刷新echarts
  chartDataGraphKey2,
  chartDataGraphKey3,
  chartDataGraphMethodIndex, // 风险节点中心性分析方法索引
} = toRefs(data);

// 风险节点中心性分析 stype == 1  根据stype调不同的接口
const handleAnalysis = async () => {
  echartsLoading.value = true;
  try {
    const result = await analysisInfoCentral({
      ...queryParams.value,
      type: queryParams.value.runWay ? 1 : 0,
    });
    if (!result.data) {
      proxy.$modal.msgWarning("暂无分析数据");
      return;
    }
    chartDataGraphMethodIndex.value = 0;
    chartDataGraphSource.value = result.data;
    chartDataGraph.value = {
      nodes: result.data[result.data.method[chartDataGraphMethodIndex.value]],
      links: result.data.links,
    };

    chartDataGraphKey.value++;
  } catch (error) {
    console.error("Error during analysis:", error);
  } finally {
    echartsLoading.value = false;
  }
};

const handleExport = async () => {
  downlLoadLoading.value = true;
  try {
    const result = await downloadReport({
      fileId: queryParams.value.fileId,
      type: stype.value,
    });
    const isBlob = blobValidate(result);
    if (!isBlob) {
      proxy.$modal.msgError("下载失败");
      return;
    }
    const blob = new Blob([result]);
    saveAs(
      blob,
      `${titleCardChart.value[stype.value]}_${new Date().getTime()}.zip`
    );
  } catch (error) {
    downlLoadLoading.value = false;
  } finally {
    downlLoadLoading.value = false;
  }
};

// 聚类 stype == 2
const handleClass = async () => {
  echartsLoading.value = true;
  try {
    const result = await analysisInfoCluster({
      ...queryParams.value,
    });
    if (!result.data) {
      proxy.$modal.msgWarning("暂无分析数据");
      return;
    }
    chartDataGraphMethodIndex.value = 0;
    chartDataGraphSource.value = result.data;
    chartDataGraph.value = {
      nodes: result.data[result.data.method[chartDataGraphMethodIndex.value]],
      links: result.data.links,
    };

    chartDataGraphKey2.value++;
  } catch (error) {
    console.error("Error during analysis:", error);
  } finally {
    echartsLoading.value = false;
  }
};

// 脆弱性分析 stype == 3
const handleAnalysis2 = async () => {
  echartsLoading.value = true;
  try {
    const [previewResult, weaknessResult] = await Promise.all([
      analysisInfoPreviewResult({ ...queryParams.value }),
      analysisInfoWeakness({ ...queryParams.value }),
    ]);

    if (!previewResult.data) {
      proxy.$modal.msgWarning("暂无预览结果数据");
    } else {
      chartDataGraphSource.value.previewUrl = previewResult.data.previewUrl;
    }

    if (!weaknessResult.data) {
      proxy.$modal.msgWarning("暂无分析结果数据");
    } else {
      // chartDataGraphSource.value.pictureList = weaknessResult.data.map(
      //   (item) => `${import.meta.env.VITE_APP_PREVIEW_URL}/${item.url}`
      // );
      chartDataGraphSource.value.pictureList = weaknessResult.data.map(
        (item) =>
          `${window.location.protocol}//${window.location.host}/file/${item.url}`
      );
    }

    chartDataGraphKey3.value++;

    console.log("chartDataGraphSource.value", chartDataGraphSource.value);
  } catch (error) {
    console.error("Error during analysis:", error);
  } finally {
    echartsLoading.value = false;
  }
};

// 调接口导出结果 stype == 3
const exportResult = () => {
  resultLoading.value = true;
  proxy
    .download(
      "system/exportResult",
      {
        ...queryParams.value,
      },
      `result_${new Date().getTime()}.xlsx`
    )
    .finally(() => {
      resultLoading.value = false;
    });
};

/** 更新聚类数据 */
const handleUpdateData = (type) => {
  chartDataGraphMethodIndex.value += type === "next" ? 1 : -1;
  const method =
    chartDataGraphSource.value.method[chartDataGraphMethodIndex.value];
  chartDataGraph.value = {
    nodes: chartDataGraphSource.value[method],
    links: chartDataGraphSource.value.links,
  };
  chartDataGraphKey.value++;
};

// 放大缩小
const handelScale = () => {
  scaleObject.value.scaleBigShow = !scaleObject.value.scaleBigShow;
  scaleObject.value.key++;
};

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadData.value.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadData.value.isUploading = true;
  queryParams.value = {
    ...queryParams.value,
    fileId: response?.data?.id,
  };
};
// const handleFileChange = (event, file, fileList) => {
//   proxy.$refs["uploadRef"].submit();
// };
const handleFileExceed = (file, fileList) => {
  proxy.$modal.msgWarning("请先删除已上传的文件");
};

const handlePreview = () => {
  const url = chartDataGraphSource.value.previewUrl;
  if (url) {
    window.open(url, "_blank");
  }
};

function handeldownloadTemplate() {
  proxy.download("organism/analysisInfo/template/download", {});
}
</script>

<style scoped lang="scss">
.home {
  min-height: calc(100vh - 170px);
  background: linear-gradient(
      1turn,
      rgba(140, 201, 56, 0.2),
      rgba(140, 201, 56, 0) 20%
    ),
    linear-gradient(180deg, #8dcfeb, #fefeff);

  .upload-file {
    margin: 10px auto 0;
    width: 100%;
    height: 355px;
  }

  :deep(.el-upload) {
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 1.5rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 1;
    background: hsla(0, 0%, 100%, 0.35);

    .el-upload-dragger {
      width: 100%;
      height: 250px;

      .el-icon--upload {
        margin-top: 10px;
      }

      .el-icon {
        height: 1.5em;
        width: 1.5em;

        svg {
          height: 100%;
          width: 100%;
        }
      }
    }

    .el-upload__text {
      font-size: 14px;
      // padding: 2px 60px;
      display: inline-block;
      width: 100%;
    }
  }

  .chart-box {
    height: 380px;
  }

  .result-box {
    min-height: 400px;
  }
}

:deep(.el-input-number .el-input__inner) {
  text-align: left; // 将光标和输入的数字放在最左边
}

.container-scale-big {
  animation: anim-open 0.4s linear forwards;
  z-index: 99999;
  height: 470px;
  position: absolute;
  // left: 80px;
  top: 50px;
  // bottom: 0;
  right: 400px;
  margin: auto;
  opacity: 0.6;
}
.container-scale-big-close {
  animation: anim-close 0.4s linear backwards;
  opacity: 1;
}
@keyframes anim-open {
  0% {
    opacity: 0;
    transform: scale(1);
  }

  60% {
    opacity: 0.4;
    transform: scale(1.4);
  }

  80% {
    opacity: 0.8;
    transform: scale(1.6);
  }

  100% {
    opacity: 1;
    transform: scale(1.5);
  }
}
@keyframes anim-close {
  0% {
    opacity: 0.4;
    transform: scale(1.5) translate(0, 0);
  }
  60% {
    opacity: 0.9;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1) translate(0, 0);
  }
}
</style>

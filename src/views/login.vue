<template>
  <div class="login">
    <!-- 语言切换组件 -->
    <div class="language-switch-container">
      <LanguageSwitch />
    </div>
    <div class="v-flex">
      <div class="left-box"></div>

      <el-form
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <div class="logo">
          <img style="width: 100%; height: 100%" :src="logoUrl" alt="logo" />
        </div>

        <h3 class="title">{{ t("login.title") }}</h3>
        <div class="sub-title">
          {{
            currentLanguage === "zh"
              ? "Invasive System Management Platform"
              : "入侵系统管理平台"
          }}
        </div>
        <div class="v-m-x-30">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              :placeholder="t('login.usernamePlaceholder')"
            >
              <template #prefix
                ><svg-icon icon-class="user" class="el-input__icon input-icon"
              /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              :placeholder="t('login.passwordPlaceholder')"
              @keyup.enter="handleLogin"
            >
              <template #prefix
                ><svg-icon
                  icon-class="password"
                  class="el-input__icon input-icon"
              /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input
              v-model="loginForm.code"
              size="large"
              auto-complete="off"
              placeholder="验证码"
              style="width: 63%"
              @keyup.enter="handleLogin"
            >
              <template #prefix
                ><svg-icon
                  icon-class="validCode"
                  class="el-input__icon input-icon"
              /></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
          <el-checkbox
            v-model="loginForm.rememberMe"
            style="margin: 0px 0px 25px 0px"
            >记住密码</el-checkbox
          >
          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              size="large"
              class="submit-btn"
              type="primary"
              style="width: 100%"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right" v-if="register">
              <router-link class="link-type" :to="'/register'"
                >立即注册</router-link
              >
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>
        <span class="v-p-r-10">中国外来入侵物种公共服务平台</span>
        Copyright © {{ dayjs().year() }} 版权所有 中国农业科学院植物保护研究所
      </span>
    </div> -->
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import useLanguageStore from "@/store/modules/language";
import { t } from "@/utils/i18n";
import LanguageSwitch from "@/components/LanguageSwitch/index.vue";
import dayjs from "dayjs";
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const languageStore = useLanguageStore();
const router = useRouter();
const { proxy } = getCurrentInstance();

const currentLanguage = computed(() => languageStore.currentLanguage);

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const title = computed(
  () => settingsStore.bigTitle || import.meta.env.VITE_APP_TITLE
);
const engTitle = computed(
  () => settingsStore.smallTitle || import.meta.env.VITE_APP_ENG_TITLE
);

const logoUrl = ref(settingsStore.logoUrl);

watch(
  () => settingsStore.logoUrl,
  (newUrl) => {
    logoUrl.value = newUrl;
  },
  { immediate: true }
);

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          router.push({ path: redirect.value || "/" });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.data.img;
      loginForm.value.uuid = res.data.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();

settingsStore.getSystemData();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("@/assets/images/login-background.png");
  background-size: cover;

  .left-box {
    height: 600px;
    width: 700px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    background: url("@/assets/images/loginImg.png") no-repeat center / 100% 100%;
  }
}
.title {
  margin: 0px auto 20px auto;
  text-align: center;
  font-weight: 700;
  font-size: 26px;
  color: #0e9639;
}

.sub-title {
  color: #d2d2d2;
  text-align: center;
  font-size: 16px;
  margin-bottom: 40px;
}

.login-form {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  background: #ffffff;
  width: 550px;
  height: 600px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }

  .logo {
    width: 100px;
    height: 80px;
    margin: 0px auto 20px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #ffffff59;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.language-switch-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.submit-btn {
  background-image: linear-gradient(90deg, #089759 0%, #015fbe 100%);
}
</style>

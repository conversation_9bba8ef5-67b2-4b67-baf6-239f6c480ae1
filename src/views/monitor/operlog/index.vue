<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" :label-width="labelWidth">
        <el-form-item :label="t('operlog.operIp')" prop="operIp">
          <el-input
            v-model="queryParams.operIp"
            :placeholder="t('operlog.operIpPlaceholder')"
            clearable
            style="width: 240px;"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('operlog.title')" prop="title">
            <el-input
               v-model="queryParams.title"
               :placeholder="t('operlog.titlePlaceholder')"
               clearable
               style="width: 240px;"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="t('operlog.operName')" prop="operName">
            <el-input
               v-model="queryParams.operName"
               :placeholder="t('operlog.operNamePlaceholder')"
               clearable
               style="width: 240px;"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="t('operlog.businessType')" prop="businessType">
            <el-select
               v-model="queryParams.businessType"
               :placeholder="t('operlog.businessTypePlaceholder')"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in sys_oper_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('common.status')" prop="status">
            <el-select
               v-model="queryParams.status"
               :placeholder="t('operlog.statusPlaceholder')"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in sys_common_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('operlog.operTime')" style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD HH:mm:ss"
               type="daterange"
               range-separator="-"
               :start-placeholder="t('operlog.startDate')"
               :end-placeholder="t('operlog.endDate')"
               :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ t('common.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ t('common.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['monitor:operlog:remove']"
            >{{ t('common.delete') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               @click="handleClean"
               v-hasPermi="['monitor:operlog:remove']"
            >{{ t('operlog.clean') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['monitor:operlog:export']"
            >{{ t('common.export') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table ref="operlogRef" v-loading="loading" :data="operlogList" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
         <el-table-column type="selection" width="50" align="center" />
         <el-table-column :label="t('operlog.operId')" align="center" prop="operId" />
         <el-table-column :label="t('operlog.title')" align="center" prop="title" :show-overflow-tooltip="true" />
         <el-table-column :label="t('operlog.businessType')" align="center" prop="businessType">
            <template #default="scope">
               <dict-tag :options="sys_oper_type" :value="scope.row.businessType" />
            </template>
         </el-table-column>
         <el-table-column :label="t('operlog.requestMethod')" align="center" prop="requestMethod" />
         <el-table-column :label="t('operlog.operName')" align="center" width="110" prop="operName" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']" />
         <el-table-column :label="t('operlog.deptName')" align="center" prop="deptName" width="130" :show-overflow-tooltip="true" />
         <el-table-column :label="t('operlog.operIp')" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
         <el-table-column :label="t('operlog.operLocation')" align="center" prop="operLocation" :show-overflow-tooltip="true" />
        <el-table-column :label="t('operlog.status')" align="center" prop="status">
            <template #default="scope">
               <dict-tag :options="sys_common_status" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('operlog.operTime')" align="center" prop="operTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
            <template #default="scope">
               <span>{{ parseTime(scope.row.operTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('operlog.costTime')" align="center" prop="costTime" width="110" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']">
            <template #default="scope">
               <span>{{ scope.row.costTime }}{{ t('operlog.millisecond') }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('common.operation')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="View" @click="handleView(scope.row, scope.index)" v-hasPermi="['monitor:operlog:query']">{{ t('operlog.detail') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 操作日志详细 -->
      <el-dialog :title="t('operlog.detailTitle')" v-model="open" width="700px" append-to-body>
         <el-form :model="form" :label-width="labelWidth">
            <el-row>
               <el-col :span="24">
                  <el-form-item :label="t('operlog.loginInfo')">
                    {{ form.operName }} / {{form.deptName}} / {{ form.operIp }} / {{ form.operLocation }}
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('operlog.requestInfo')">{{ form.requestMethod }} {{form.operUrl }}</el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('operlog.operModule')">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('operlog.operMethod')">{{ form.method }}</el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('operlog.operParam')">{{ form.operParam }}</el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('operlog.jsonResult')">{{ form.jsonResult }}</el-form-item>
               </el-col>
               <el-col :span="6">
                  <el-form-item :label="t('operlog.status')">
                     <div v-if="form.status === 0">{{ t('operlog.normal') }}</div>
                     <div v-else-if="form.status === 1">{{ t('operlog.fail') }}</div>
                  </el-form-item>
               </el-col>
               <el-col :span="8">
                  <el-form-item :label="t('operlog.costTime')">{{ form.costTime }}{{ t('operlog.millisecond') }}</el-form-item>
               </el-col>
               <el-col :span="10">
                  <el-form-item :label="t('operlog.operTime')">{{ parseTime(form.operTime) }}</el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('operlog.errorMsg')" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button @click="open = false">{{ t('common.close') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Operlog">
import { ref, reactive, computed, getCurrentInstance } from "vue";
import { t } from '@/utils/i18n';
import { list, delOperlog, cleanOperlog } from "@/api/monitor/operlog";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from '@/composables/useLanguageChange'

const { proxy } = getCurrentInstance();
const { sys_oper_type, sys_common_status } = proxy.useDict("sys_oper_type","sys_common_status");
const languageStore = useLanguageStore();

const labelWidth = computed(() => {
  return languageStore.currentLanguage === 'zh' ? '68px' : '120px';
});

const operlogList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const defaultSort = ref({ prop: "operTime", order: "descending" });

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operIp: undefined,
    title: undefined,
    operName: undefined,
    businessType: undefined,
    status: undefined
  }
});

const { queryParams, form } = toRefs(data);

/** 查询登录日志 */
function getList() {
  loading.value = true;
  list(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    operlogList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 操作日志类型字典翻译 */
function typeFormat(row, column) {
  return proxy.selectDictLabel(sys_oper_type.value, row.businessType);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  proxy.$refs["operlogRef"].sort(defaultSort.value.prop, defaultSort.value.order);
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.operId);
  multiple.value = !selection.length;
  single.value = selection.length != 1;
}

/** 排序触发事件 */
function handleSortChange(column, prop, order) {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
}

/** 详细按钮操作 */
function handleView(row) {
  open.value = true;
  form.value = row;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const operIds = row.operId || ids.value;
  proxy.$modal.confirm(t('operlog.deleteConfirm', { operIds })).then(function() {
    return delOperlog(operIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(t('common.deleteSuccess'));
  }).catch(() => {});
}

/** 清空按钮操作 */
function handleClean() {
  proxy.$modal.confirm(t('operlog.cleanConfirm')).then(function() {
    return cleanOperlog();
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(t('operlog.cleanSuccess'));
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("monitor/operlog/export", {
    ...queryParams.value,
  }, `operlog_${new Date().getTime()}.xlsx`);
}

// Add language change handling
useLanguageChange()

getList();
</script>

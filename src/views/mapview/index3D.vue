<template>
  <div class="home">
    <div id="cesiumContainer" class="cesium-container"></div>
  </div>
</template>

<script setup name="Index">
const DEFAULT_ACCESS_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIxMWRjMjYxMi1iY2UzLTRiNGUtODM2Mi05NzA1ZWFiNDAzNzYiLCJpZCI6Mjc4OTU3LCJpYXQiOjE3NDA0NzU3NTN9.dRe55tc4T3mnr3LbCi73kPeCY29dkB97RjFv9t7qGos";
import { onMounted } from "vue";
import * as Cesium from "cesium";
import geoJsonData from "./China.json";
import bugIcon from "@/assets/images/wuzhong.png";
import wuzhongIcon from "@/assets/images/wuzhong.png";
import { listSpecies } from "@/api/species";
const route = useRoute();
// Mock 数据
const mockPoints = [
  { lon: 116.4074, lat: 39.9042, name: "北京", description: "北京市" },
  { lon: 121.4737, lat: 31.2304, name: "上海", description: "上海市" },
  { lon: 114.3055, lat: 30.5928, name: "武汉", description: "武汉市" },
  { lon: 113.2644, lat: 23.1291, name: "广州", description: "广州市" },
  { lon: 104.0665, lat: 30.5723, name: "成都", description: "成都市" },
  { lon: 108.9402, lat: 34.3416, name: "西安", description: "西安市" },
  { lon: 117.283, lat: 31.8612, name: "合肥", description: "合肥市" },
  { lon: 118.7969, lat: 32.0603, name: "南京", description: "南京市" },
  { lon: 106.5516, lat: 29.563, name: "重庆", description: "重庆市" },
  { lon: 114.1694, lat: 22.3193, name: "香港", description: "香港特别行政区" },
  { lon: 113.5439, lat: 22.1987, name: "澳门", description: "澳门特别行政区" },
  { lon: 121.5654, lat: 25.033, name: "台北", description: "台北市" },
  { lon: 120.1551, lat: 30.2741, name: "杭州", description: "杭州市" },
  { lon: 113.2806, lat: 23.1252, name: "佛山", description: "佛山市" },
  { lon: 119.2965, lat: 26.0745, name: "福州", description: "福州市" },
  { lon: 118.0894, lat: 24.4798, name: "厦门", description: "厦门市" },
  { lon: 113.1227, lat: 23.0288, name: "东莞", description: "东莞市" },
  { lon: 113.7633, lat: 23.043, name: "中山", description: "中山市" },
  { lon: 113.3824, lat: 22.5211, name: "珠海", description: "珠海市" },
  { lon: 113.7518, lat: 23.0207, name: "惠州", description: "惠州市" },
];

// 初始化 Cesium
const initCesium = async () => {
  // 初始化 Cesium Viewer
  Cesium.Ion.defaultAccessToken = DEFAULT_ACCESS_TOKEN;
  const viewer = new Cesium.Viewer("cesiumContainer", {
    // baseLayerPicker: false,
    // geocoder: false,
    // homeButton: false,
    // sceneModePicker: false,
    // navigationHelpButton: false,
    // animation: false,
    // timeline: false,
    // fullscreenButton: false,
    // infoBox: false,
    baseLayerPicker: false, // 如果设置为false，将不会创建右上角图层按钮
    geocoder: false, // 如果设置为false，将不会创建右上角查询(放大镜)按钮
    navigationHelpButton: false, // 如果设置为false，则不会创建右上角帮助(问号)按钮
    homeButton: false, // 如果设置为false，将不会创建右上角主页(房子)按钮
    sceneModePicker: false, // 如果设置为false，将不会创建右上角投影方式控件(显示二三维切换按钮)
    animation: false, // 如果设置为false，将不会创建左下角动画小部件
    timeline: false, // 如果设置为false，则不会创建正下方时间轴小部件
    fullscreenButton: false, // 如果设置为false，将不会创建右下角全屏按钮
    scene3DOnly: true, // 为 true 时，每个几何实例将仅以3D渲染以节省GPU内存
    shouldAnimate: false, // 默认true ，否则为 false 。此选项优先于设置 Viewer＃clockViewModel
    infoBox: false, // 是否显示点击要素之后显示的信息
    sceneMode: 3, // 初始场景模式 1 2D模式 2 2D循环模式 3 3D模式  Cesium.SceneMode
    requestRenderMode: false, // 启用请求渲染模式，不需要渲染，节约资源
  });

  //天地图 矢量地图
  var td_vec_w =
    "http://{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0" +
    "&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}" +
    "&style=default&format=tiles&tk=31f0eb7d629d7b6fa27ce133ac024cf4";

  var vecProvider = new window.Cesium.WebMapTileServiceImageryProvider({
    url: td_vec_w,
    layer: "vec_w",
    style: "default",
    format: "tiles",
    tileMatrixSetID: "image/jpeg",
    subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
    minimumLevel: 0,
    maximumLevel: 18,
  });

  //天地图 矢量注记
  var td_cva_w =
    "http://{s}.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0" +
    "&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}" +
    "&style=default&format=tiles&tk=31f0eb7d629d7b6fa27ce133ac024cf4";

  var cvaProvider = new window.Cesium.WebMapTileServiceImageryProvider({
    url: td_cva_w,
    layer: "cva_w",
    style: "default",
    format: "tiles",
    tileMatrixSetID: "image/jpeg",
    subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
    minimumLevel: 0,
    maximumLevel: 18,
  });

  //叠加多个影像图层
  viewer.imageryLayers.addImageryProvider(vecProvider);
  viewer.imageryLayers.addImageryProvider(cvaProvider);

  // 设置视角到中国
  viewer.camera.setView({
    destination: Cesium.Rectangle.fromDegrees(0, 0, 0, 0), // 中国的经纬度范围
    orientation: {
      heading: Cesium.Math.toRadians(0.0), // 朝北
      pitch: Cesium.Math.toRadians(-90.0), // 俯视
      roll: 0.0,
    },
  });

  // 加载并绘制中国边界
  Cesium.GeoJsonDataSource.load(geoJsonData, {
    stroke: Cesium.Color.RED,
    fill: Cesium.Color.RED.withAlpha(0.06),
    strokeWidth: 5,
    markerSymbol: "?",
  })
    .then((dataSource) => {
      // viewer.dataSources.add(dataSource);
      // 使用动画将视角调整到平铺中国地图
      viewer.camera.flyTo({
        destination: Cesium.Rectangle.fromDegrees(73.66, 18.11, 134.77, 53.55), // 中国的经纬度范围
        orientation: {
          heading: Cesium.Math.toRadians(0.5), // 朝北
          pitch: Cesium.Math.toRadians(-90.0), // 俯视
          roll: 0.0,
        },
        duration: 3, // 动画持续时间（秒）
      });

      viewer._cesiumWidget._creditContainer.style.display = "none"; // 隐藏版权
    })
    .catch((error) => {
      console.error("Error loading GeoJSON data:", error);
    });

  // 添加点到地图并启用聚合
  const dataSource = new Cesium.CustomDataSource("points");

  const { rows } = await listSpecies({
    pageNum: 1,
    pageSize: 99999,
  });

  rows.forEach((point, index) => {
    if (isNaN(parseFloat(point.longitude)) || isNaN(parseFloat(point.latitude)))
      return;
    dataSource.entities.add({
      position: Cesium.Cartesian3.fromDegrees(
        +point.longitude,
        +point.latitude
      ),
      billboard: {
        image: route.query.stype == 2 ? bugIcon : wuzhongIcon,
        width: 32,
        height: 32,
      },
      properties: {
        id: index,
        name: point.name,
        description: point.description,
      },
    });
  });
  viewer.dataSources.add(dataSource);

  // 启用聚合
  dataSource.clustering.enabled = true;
  dataSource.clustering.pixelRange = 15;
  dataSource.clustering.minimumClusterSize = 3;

  // 自定义聚合点样式
  dataSource.clustering.clusterEvent.addEventListener(
    (clusteredEntities, cluster) => {
      cluster.label.show = true;
      cluster.label.text = clusteredEntities.length.toString();
      cluster.label.font = "20px sans-serif";
      cluster.label.fillColor = Cesium.Color.WHITE;
      cluster.label.outlineColor = Cesium.Color.BLACK;
      cluster.label.outlineWidth = 14;
      cluster.billboard.show = true;
      cluster.billboard.image = route.query.stype == 2 ? bugIcon : wuzhongIcon;
      cluster.billboard.width = 48;
      cluster.billboard.height = 48;
    }
  );

  // 添加点击事件
  const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction((movement) => {
    const pickedObject = viewer.scene.pick(movement.position);
    if (Cesium.defined(pickedObject) && pickedObject.id) {
      const entity = pickedObject.id;
      if (entity.isCluster) {
        // 如果是聚合点，缩放一级
        viewer.camera.flyTo({
          destination: viewer.camera.getRectangleCameraCoordinates(
            entity.rectangle
          ),
          duration: 1.5,
        });
      } else {
        // 如果是单个点，显示详情
        viewer.selectedEntity = entity;
        console.log(
          `名称: ${entity?.properties?.name.getValue()}\n描述: ${entity?.properties?.description?.getValue()}`
        );
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

onMounted(() => {
  initCesium();
});
</script>

<style scoped lang="scss">
.home {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.cesium-container {
  height: 100%;
  width: 100%;
}
</style>

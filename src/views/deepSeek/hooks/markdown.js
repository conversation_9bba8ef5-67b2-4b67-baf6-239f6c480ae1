// import { unified } from "unified";
// import remarkParse from "remark-parse";
// import remarkGfm from "remark-gfm";
// import remarkMath from "remark-math";
// import remarkBreaks from "remark-breaks";
// import remarkRehype from "remark-rehype";
// import rehypeKatex from "rehype-katex";
// import rehypeStringify from "rehype-stringify";

import { marked } from "marked";
import "katex/dist/katex.min.css";
import "github-markdown-css/github-markdown.css"; // GitHub 样式
import hljs from "highlight.js";
import "highlight.js/styles/monokai-sublime.css";
//未用到
// export async function processMarkdown(content) {
//   try {
//     const file = await unified()
//       .use(remarkParse) // 解析 Markdown
//       .use(remarkBreaks) // 换行支持
//       .use(remarkGfm) // GitHub 风格
//       .use(remarkMath) // 数学公式
//       .use(remarkRehype, {
//         // 转换到 HTML 处理
//         allowDangerousHtml: true, // 允许原始 HTML
//       })
//       .use(rehypeKatex) // 数学公式渲染
//       .use(rehypeStringify) // 生成 HTML
//       .process(content);

//     return file.toString();
//   } catch (e) {
//     console.error("Markdown 处理失败:", e);
//     return '<div class="error">内容渲染错误</div>';
//   }
// }

export function useMarkdown() {
  const renderer = new marked.Renderer();

  marked.setOptions({
    renderer: renderer,
    gfm: true,
    pedantic: false, // 严格模式，忽略一些无效的 HTML 标签
    sanitize: true,
    tables: true,
    breaks: true,
    smartLists: true,
    smartypants: true,
    highlight: function (code) {
      return hljs.highlightAuto(code).value;
    },
  });
  function formatMarkTable(txt) {
    return txt
      .split("\n")
      .map((line) => {
        // 过滤纯空格行
        if (/^\s*$/.test(line)) return "";

        // 清理管道符周围空格
        return line
          .trim()
          .replace(/\s*\|\s*/g, "|") // 管道符周围空格
          .replace(/\|+/g, "|") // 连续管道符
          .replace(/^\||\|$/g, ""); // 首尾管道符
      })
      .join("\n");
  }

  return { marked, formatMarkTable };
}

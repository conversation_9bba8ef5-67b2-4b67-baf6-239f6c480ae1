export function useInput(askContent, handleSubmit) {
  const inputRef = ref(null);
  function handleKeydown(event) {
    if (event.key === "Enter") {
      // Shift + Enter 换行
      if (event.shiftKey) {
        insertNewline();
        event.preventDefault(); // 阻止默认换行行为
      }
      // 单独 Enter 提交
      else {
        handleSubmit();
        event.preventDefault();
      }
    }
  }
  async function insertNewline() {
    // 手动插入换行符（兼容所有浏览器）
    const textarea = inputRef.value?.textarea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    askContent.value = askContent.value.slice(0, start) + "\n" + askContent.value.slice(end);

    // 更新光标位置
    await nextTick();
    textarea.selectionStart = start + 1;
    textarea.selectionEnd = start + 1;
  }
  return {
    inputRef,
    handleKeydown,
    insertNewline,
  };
}

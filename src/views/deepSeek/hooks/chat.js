import dayjs from "dayjs";
import { StreamFetcher } from "../lib";
import { ElMessage } from "element-plus";
import { generatorUUID } from "@/utils/index";
import { deepClone } from "@/utils";
export const CHAT_ANSWER = "answer";
export const CHAT_FLOWNODE = "flowNodeStatus";
export const CHAT_FLOWRESPONSE = "flowResponses";
export const CHAT_STATUS = {
  RUNNING: "running",
  DONE: "done",
  READY: "ready",
};
export const CHAT_ROLE_TYPE = {
  HUMAN: "Human",
  AI: "AI",
};
export function getChatInfo(uerType, content = "") {
  return {
    dataId: generatorUUID(),
    _id: generatorUUID(),
    time: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    obj: uerType,
    value: [{ type: "text", text: { content } }],
    hideInUI: false,
    totalRunningTime: 0,
  };
}
const aiResponse = {
  ...getChatInfo(CHAT_ROLE_TYPE.AI),
  historyPreviewLength: 0,
  responseNodeList: [],
  llmModuleAccount: 1,
  totalQuoteList: [],
  totalRunningTime: 0,
  userGoodFeedback: false,
  userBadFeedback: "",
  abort: false,
};

export function useChatMessage(props, emits) {
  const askContent = ref("");
  const chatList = ref([]);
  const chatStatus = reactive({
    text: "加载中...",
    code: CHAT_STATUS.READY,
  });
  const currentAiResponse = ref({
    ...aiResponse,
  });

  function chatStart() {
    if (chatStatus.code === CHAT_STATUS.RUNNING) {
      // ElMessage({ message: "聊天正在进行，请等待结束", type: "error" });
      return;
    }
    const askText = askContent.value.trim();
    askContent.value = "";
    chatStatus.code = CHAT_STATUS.READY;
    currentAiResponse.value = deepClone(aiResponse);
    chatList.value.push(getChatInfo(CHAT_ROLE_TYPE.HUMAN, askText), currentAiResponse.value);
    askContent.value = "";
    emits("updateChatHis", { chatId: props.chatInfo.chatId, askContent: askContent.value });
    nextTick(() => {
      scrollToBottom();
    });
    // console.log("chatList.value++++", chatList.value);
    streamFetcher.start(
      {
        url: "/api/v1/chat/completions",
        options: {
          stream: true,
          detail: true,
          responseChatItemId: currentAiResponse.value.dataId,
          chatId: props.chatInfo.chatId,
          messages: [{ role: "user", content: askText }],
        },
      },
      (obj) => {
        const answerType = obj?.event ?? "";
        switch (answerType) {
          case CHAT_ANSWER:
            currentAiResponse.value.value[0].text.content += obj.data.choices[0].delta?.content ?? "";
            break;
          case CHAT_FLOWNODE:
            chatStatus.code = CHAT_STATUS.RUNNING;
            chatStatus.text = obj.data?.name ?? "";
            break;
          case CHAT_FLOWRESPONSE:
            currentAiResponse.value.responseNodeList = obj.data;
            const ctxPreview = obj.data.find((_) => _.historyPreview && _.historyPreview.length);
            currentAiResponse.value.historyPreviewLength = ctxPreview && (ctxPreview.historyPreview.length ?? 0);
            currentAiResponse.value.totalQuoteList = obj.data.find((_) => _.quoteList)?.quoteList ?? [];
            currentAiResponse.value.totalRunningTime = obj.data
              .reduce((prev, cur) => {
                return prev + cur?.runningTime ?? 0;
              }, 0)
              .toFixed(2);
            break;
          default:
            break;
        }
        scrollToBottom();
      },
      () => {
        chatStatus.code = CHAT_STATUS.READY;
        console.log("结束");
        scrollToBottom();
      },
      (err) => {
        console.log("错误", err);
      }
    );
  }
  const quote = reactive({
    show: false,
    type: "",
    dataId: "",
    totalQuoteList: [],
  });
  function showQuote(quoteLis, message) {
    if (!message.totalQuoteList || message.totalQuoteList.length === 0) {
      return ElMessage({
        message: "暂无引用内容,请查看问题",
        type: "error",
      });
    }
    quote.type = "";
    quote.dataId = message.dataId;
    quote.totalQuoteList = quoteLis;
    quote.show = true;
  }
  function showCollectQuote(quoteItem, message) {
    quote.type = "collect";
    quote.dataId = message.dataId;
    quote.totalQuoteList = [quoteItem];
    quote.show = true;
  }

  const streamFetcher = new StreamFetcher();
  const scrollRef = ref(null);
  const scrollToBottom = () => {
    scrollRef.value.scrollTo({
      top: scrollRef.value.scrollHeight + 50,
      behavior: "smooth",
    });
  };
  async function abortChat() {
    streamFetcher.stop();
    currentAiResponse.value.abort = true;
    await nextTick();
    chatStatus.code = CHAT_STATUS.READY;
  }

  return {
    scrollRef,
    askContent,
    chatList,
    chatStatus,
    quote,
    showQuote,
    showCollectQuote,
    chatStart,
    abortChat,
    scrollToBottom,
  };
}

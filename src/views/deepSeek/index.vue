<template>
  <div class="chat-wrapper">
    <div class="content">
      <sidebar :style="{ '-sWidth': sidebarInfo.collapse ? '290px' : '0px' }" :chatInfo="chatInfo" :sidebarInfo="sidebarInfo" :appId="appId" ref="sidebarRef" @chooseHisChat="chooseHisChat" />
      <div class="chat-content" v-loading="sidebarInfo.loading" ref="contentRef">
        <div class="new-chat-ask-box" v-if="!chatInfo.chatId">
          <div class="flex-row ai-intro-box">
            <div class="img">
              <img style="zoom: 0.15" src="@/assets/images/logo.png" alt="" />
            </div>
            <div class="text-intro">
              <div class="name">我是 小wan，专注生物入侵风险评估的AI助手</div>
              <div class="text-14px mt-10px func">我可以帮你查询物种分布、地理分布、跨境传入风险、定殖适生风险、扩散风险分析等信息，快点儿输入吧~</div>
            </div>
          </div>
          <div class="chat-bottom">
            <div class="ask-box">
              <el-input ref="inputRef" @keydown="handleKeydown" v-model="askContent" class="ask-input" autosize :resize="0" type="textarea" placeholder="有问题，尽管问,发送[shift+enter]换行" />
              <div @click="startAsKInfo" title="发送" class="send-btn flex-center">
                <i class="iconfont sw-fasong"></i>
              </div>
            </div>
          </div>
        </div>
        <chat @updateChatHis="updateChatHis" :chatInfo="chatInfo" ref="chatRef" v-else></chat>
      </div>
    </div>
    <!-- <ai></ai> -->
  </div>
</template>

<script setup>
import { generatorUUID } from "@/utils/index";
import Sidebar from "./components/Sidebar.vue";
import Chat from "./components/Chat.vue";
// import Ai from "@/components/Ai/index.vue";
import { getAppId } from "@/api/chat/fastGpt";
import { useInput } from "./hooks/input";
import useAppStore from "@/store/modules/app";
const uAppStore = useAppStore();
const chatRef = ref(null);
const chatInfo = reactive({
  chatId: "",
  appId: "",
  chatTitle: "",
});

const sidebarInfo = reactive({
  collapse: false,
  loading: true,
});
function chooseHisChat(chat) {
  if (!chat) {
  }
  chatInfo.chatId = chat?.chatId ?? "";
  chatInfo.chatTitle = chat?.customTitle || chat?.title || "";
  nextTick(() => {
    chatRef.value?.getHisChatMessageListByChatId();
  });
}
const sidebarRef = ref(null);

function updateChatHis({ chatId, askContent }) {
  sidebarRef.value.updataChatHis(chatId, askContent);
}
function getChatAppId() {
  getAppId().then((res) => {
    console.log(res);
    chatInfo.appId = res.data;
  });
}
const askContent = ref("");
async function startAsKInfo() {
  chatInfo.chatId = generatorUUID();
  await nextTick();
  sidebarRef.value.updataChatHis(chatInfo.chatId, askContent.value);
  chatRef.value.exposeChatStart(askContent.value);
  askContent.value = "";
}
const { inputRef, handleKeydown } = useInput(askContent, startAsKInfo);
getChatAppId();
uAppStore.closeSideBar({ withoutAnimation: 0 });
onUnmounted(() => {
  uAppStore.openSideBar({ withoutAnimation: 0 });
});
</script>

<style scoped lang="scss">
@import "@/assets/styles/variables.module.scss";
.chat-wrapper {
  width: 100%;
  height: calc(100vh - $base-navbar-height - 34px - 50px);
  padding: 16px;
  position: relative;

  .content {
    display: flex;

    height: 100%;
    width: 100%;
    border-radius: 16px;
    .chat-content {
      flex: 1 0 0;
      display: flex;
      background-color: #fff;
      max-width: calc(100% - var(--sWidth));

      // border: solid 1px red;
    }
  }
}
</style>
<style lang="scss">
@import "@/assets/styles/variables.module.scss";
.new-chat-ask-box {
  margin: auto;
}
.ai-intro-box {
  margin: 10px auto;
  max-width: min(800px, 100%);
  width: 100%;
  column-gap: 20px;
  .text-intro {
    display: flex;
    flex-direction: column;
    row-gap: 5px;
  }
  .name {
    font-size: 20px;
    font-weight: 600;
  }
  .func {
    font-size: 14px;
  }
}

.chat-bottom {
  margin: 10px auto;
  max-width: min(800px, 100%);
  width: 100%;
  padding: 0 20px;
  .ask-box {
    // padding-top: 18px;
    // padding-bottom: 18px;
    padding: 18px;
    border-radius: 4px;
    width: 100%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.12);
    display: flex;
    align-items: flex-end;

    .send-btn {
      height: 32px;
      width: 32px;
      border-radius: 4px;
      background-color: $--color-primary;
      color: #fff;
      cursor: pointer;
    }
    .pause-btn {
      animation: scale 1s linear infinite;
    }
    @keyframes scale {
      0% {
        transform: scale(1);
      }
      100% {
        transform: scale(1.2);
      }
    }
  }
}

.chat-el-dialog.el-dialog:not(.is-fullscreen) {
  margin-top: 22vh !important;
}
.chat-el-dialog {
  border-radius: 8px;
  .el-dialog__header {
    padding: 0 !important;
    margin-right: 0;
  }
  .el-dialog__body {
    padding: 0;
  }

  .ctx-dialog-header {
    height: 53px;
    background-color: #fbfbfc;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding-left: 16px;
    display: flex;
    align-items: center;
    column-gap: 5px;
    border-bottom: solid 1px #f4f6f8;
    font-size: 16px;
    > i {
      color: #2f83e2;
      font-size: 20px;
    }
  }
}
</style>

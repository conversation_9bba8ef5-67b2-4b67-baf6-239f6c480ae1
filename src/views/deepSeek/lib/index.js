import { getToken } from "@/utils/auth";
export class StreamFetcher {
  constructor() {
    this.abortController = null;
    this.reader = null;
    this.isFetching = false;
  }

  async start({ url, options = {} }, onData, onDone, onError) {
    await this.stop();
    this.abortController = new AbortController();
    this.isFetching = true;
    try {
      const response = await fetch(import.meta.env.VITE_APP_BASE_API + url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + getToken(),
        },
        body: JSON.stringify(options),
      });
      this.reader = response.body.getReader();
      const decoder = new TextDecoder();
      let textDecoder = "";
      while (this.isFetching) {
        const { done, value } = await this.reader.read();
        if (done) {
          this.isFetching = false;
          onDone?.();
          break;
        }

        const txt = decoder.decode(value, { stream: true });
        if (txt.trim() === "") continue;
        const textObjs = this.formateStreamData(txt);
        textObjs.forEach((objtxt) => {
          objtxt.includes;
          try {
            const jsonStr = textDecoder || objtxt;
            const jsonData = JSON.parse(jsonStr);
            onData(jsonData);
          } catch (error) {
            textDecoder += objtxt;
            try {
              const jsonData = JSON.parse(textDecoder);
              textDecoder = "";
              onData(jsonData);
            } catch (error) {}
          }
        });
      }
    } catch (err) {
      // 过滤主动终止的异常
      if (err.name !== "AbortError") {
        onError?.(err);
      }
    } finally {
      this.cleanup();
    }
  }

  formateStreamData(strData) {
    const trimData = strData.trim();
    // console.log('trimData', trimData)
    const dataStart = trimData.split("data:");
    return dataStart.filter((_) => _).map((_) => _.trim());
  }

  /**
   * 强制终止请求和流读取
   */
  async stop() {
    if (!this.isFetching) return;

    // 1. 终止 fetch 请求
    this.abortController?.abort();

    // 2. 关闭流读取器
    if (this.reader) {
      await this.reader.cancel();
      this.reader = null;
    }

    this.isFetching = false;
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.abortController = null;
    this.reader = null;
    this.isFetching = false;
  }
}

export function formatStream(data) {}

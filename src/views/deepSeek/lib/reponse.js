export const detailIconList = [
  {
    color: "rgb(104, 192, 255)",
    icon: "sw-fasong",
  },
  {
    color: "rgb(84, 184, 255)",
    icon: "sw-zhis<PERSON><PERSON>",
  },
  {
    color: "rgb(123, 148, 255)",
    icon: "sw-AIzhus<PERSON>",
  },
];
export const startNodes = [
  { name: "模型名", key: "moduleName" },
  { name: "运行时长", key: "runningTime" },
];
export const searchNodes = [
  { name: "模型名", key: "moduleName" },
  { name: "运行时长", key: "runningTime" },
  { name: "模型", key: "model" },
  { name: "输入Tokens", key: "inputTokens" },
  { name: "问题/检索词", key: "query" },
  { name: "搜索方式", key: "runningTime", value: "语义检索" },
  { name: "相似度", key: "similarity" },
  { name: "结果重排", key: "searchUsingReRank" },
  { name: "问题优化模型", key: "queryExtensionResult", subKey: "model" },
  { name: "问题优化输入/输出Tokens", key: "spe" },
  {
    name: "问题优化结果",
    key: "queryExtensionResult",
    subKey: "query",
    isHtml: true,
  },
];
export const aiNodes = [
  { name: "模型名", key: "moduleName" },
  { name: "运行时长", key: "runningTime" },
  { name: "模型", key: "model" },
  { name: "AI Tokens总量", key: "tokens" },
  { name: "输入Tokens", key: "inputTokens" },
  { name: "输出Tokens", key: "outputTokens" },
  { name: "问题/检索词", key: "query" },
  { name: "上下文总长度", key: "contextTotalLen" },
  { name: "最大响应tokens", key: "maxToken" },
  { name: "完成原因", key: "similarity", value: "正常完成" },
];

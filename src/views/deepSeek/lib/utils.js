import dayjs from "dayjs";

export const formatTimeToChatTime = (time) => {
  const now = dayjs();
  const target = dayjs(time);

  // 如果传入时间小于60秒，返回刚刚
  if (now.diff(target, "second") < 60) {
    return "刚刚";
  }

  // 如果时间是今天，展示几时:几分
  //用#占位，i18n生效后replace成:
  if (now.isSame(target, "day")) {
    return target.format("HH:mm");
  }

  // 如果是昨天，展示昨天
  if (now.subtract(1, "day").isSame(target, "day")) {
    return "昨天";
  }

  // 如果是今年，展示某月某日
  if (now.isSame(target, "year")) {
    return target.format("MM-DD");
  }

  // 如果是更久之前，展示某年某月某日
  return target.format("YYYY-M-D");
};

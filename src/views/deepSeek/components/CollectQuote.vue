<template>
  <div class="quote" v-loading="loading">
    <div class="q-header flex-between">
      <div class="name-info">
        <div class="flex-row name">
          <i class="iconfont sw-yingxiaozhongxin-yingxiaogongju-dazhuanpan-chuangjianhuodong-xlsx"></i>
          <span>{{ getSourceName }}</span>
          <!-- <el-popover popper-class="down-popover" :show-arrow="false" placement="bottom-start" trigger="hover">
            <template #reference>
              <div class="down-btn flex-center">
                <i class="iconfont sw-xiazai"></i>
              </div>
            </template>
            <div class="btn-box">
              <div @click="downloadData">下载数据</div>
              <div @click="openFile">打开原文</div>
            </div>
          </el-popover> -->
        </div>
        <div class="notice">来源知识库: 生物入侵跨境传入与发生扩散风险分析</div>
      </div>
      <div class="close" @click="emit('close')">
        <i class="iconfont sw-close"></i>
      </div>
    </div>
    <div class="notice-text">此处仅显示实际引用内容，若数据有更新，此处不会实时更新</div>
    <div class="quote-content">
      <el-scrollbar class="scrollbar" style="height: 100%">
        <div style="padding: 0 20px" v-for="(item, index) in totalQuoteList" :key="index">
          <div class="file-use">
            <div class="file-use-item flex-row">
              <div class="idx flex-center">{{ index + 1 }}</div>
              <div class="file-info flex-row">
                <i class="iconfont sw-wendang"></i>
                <div class="file-name">{{ item.sourceName }}</div>
              </div>
            </div>
          </div>
          <!-- {{ quoteList[index].q }} -->
          <div class="mark-value markdown Markdown_markdown__h2C_t" v-html="getTextVal(quoteList[index]?.q)"></div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { getCollectionQuote } from "@/api/chat/fastGpt";
import { useMarkdown } from "../hooks/markdown";
const props = defineProps({
  totalQuoteList: [],
  appId: "",
  chatId: "",
  dataId: "",
});
watch(
  () => props.totalQuoteList,
  () => {
    getQuoteInfo();
  },
  { deep: true }
);

const { marked, formatMarkTable } = useMarkdown();
const loading = ref(true);
const quoteList = ref([]);
function getQuoteInfo() {
  const request = {
    appId: props.appId,
    chatId: props.chatId,
    chatItemDataId: props.dataId,
    collectionId: props.totalQuoteList[0].collectionId,
    initialId: props.totalQuoteList[0].sourceId,
    initialIndex: 1,
    pageSize: 30,
  };
  getCollectionQuote(request).then((res) => {
    console.log("res-----", res);
    quoteList.value = res.data.list;
    loading.value = false;
  });
}
const getSourceName = computed(() => {
  return (props.totalQuoteList.length && props.totalQuoteList[0]?.sourceName) || "";
});

getQuoteInfo();
const getTextVal = computed(() => {
  return (item) => {
    let value = item ?? "";
    value = formatMarkTable(value);

    return marked(value);
  };
});
function downloadData() {}
function openFile() {}
const emit = defineEmits(["close"]);
</script>

<style lang="scss">
@import "./index.module.scss";
@import "@/assets/styles/variables.module.scss";
.quote {
  height: 100%;
  .q-header {
    height: 78px;
    background-color: #fbfbfc;
    display: flex;
    align-items: center;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 0 20px;
    border-bottom: solid 1px #eee;
    .name-info {
      display: flex;
      flex-direction: column;
      row-gap: 5px;
      .name {
        column-gap: 10px;
        > i {
          color: #3370ff;
        }
      }
      .notice {
        font-size: 12px;
        color: #999;
      }
    }
    .close {
      cursor: pointer;
    }
  }
  .notice-text {
    font-size: 12px;
    color: #999;
    height: 30px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    border-bottom: solid 1px #eee;
  }
  .quote-content {
    height: calc(100% - 108px);
    width: 100%;
    padding: 20px 0;
  }
  .file-use {
    width: fit-content;
    .file-use-item {
      border: 1px solid #e8ebf0;
      border-radius: 6px;
      // padding: 10px;
      margin: 10px 0;
      height: 22px;
      .idx {
        width: 16px;
        height: 100%;
        background-color: #f0f1f6;
        font-size: 12px;
      }
      .file-info {
        padding: 0 7px;
        column-gap: 5px;
        > i {
          color: rgb(69, 176, 88);
        }
        .file-name {
          font-size: 12px;
        }
      }
    }
  }
}
.down-popover.el-popper {
  padding: 4px;
  border-radius: 4px;
  min-width: unset;
  width: 100px !important;
  .btn-box {
    > div {
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background-color: #e6e5e5;
        color: $--color-primary;
      }
    }
  }
}
</style>

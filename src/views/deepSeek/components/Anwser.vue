<template>
  <div class="anwser">
    <div class="chat-op-box flex-row">
      <div class="avatar flex-center">
        <div class="circle flex-center">
          <i class="iconfont sw-data-Inquire-full"></i>
        </div>
      </div>
      <template v-if="anwser.totalRunningTime || anwser.abort">
        <div class="op-box flex-row">
          <div class="op-item flex-center" @click="emits('copy', anwser.value[0].text.content)">
            <i class="iconfont sw-fuzhi"></i>
          </div>
          <div v-if="!anwser.userBadFeedback" :class="{ 'feed-back-btn': anwser.userGoodFeedback }" class="op-item flex-center" @click="emits('approveFeedback')">
            <i class="iconfont sw-dianzan"></i>
          </div>
          <div v-if="!anwser.userGoodFeedback" :class="{ 'un-feed-back-btn': anwser.userBadFeedback }" class="op-item flex-center" @click="emits('unApproveFeedback')">
            <i class="iconfont sw-caizan"></i>
          </div>
        </div>
      </template>
      <div class="status-box flex-row" :class="[`status-type-${chatStatus.code}`]" v-else>
        <div class="dot"></div>
        <div>{{ chatStatus.text }}</div>
      </div>
    </div>
    <div class="anwser-body markdown Markdown_markdown__h2C_t">
      <div>
        <div class="mark-info" v-html="textVal"></div>
      </div>
      <template v-if="anwser.totalRunningTime">
        <template v-if="anwser.totalQuoteList.length">
          <div class="use-sign flex-row">
            <i class="iconfont sw-yinyong"></i>
            <span>引用</span>
            <div class="line"></div>
          </div>
          <div class="flex-row quote-wrapper">
            <div v-for="(item, index) in getQuoteList" :key="item.id" @click="emits('showCollectQuote', item)" class="file-use">
              <div class="file-use-item flex-row">
                <div class="idx flex-center">{{ index + 1 }}</div>
                <div class="file-info flex-row">
                  <i class="iconfont sw-wendang"></i>
                  <div class="file-name">{{ item.sourceName }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div class="other-info">
          <div class="use-btn" @click="viewQuote" v-if="anwser.totalQuoteList.length" :class="[`type-1`]">{{ getQuoteList.length }}条引用</div>
          <div class="use-btn" @click="viewCtx" :class="[`type-2`]">{{ anwser.historyPreviewLength }}条上下文</div>
          <div class="use-btn" :class="[`type-3`]">{{ anwser.totalRunningTime }}s</div>
          <!-- <div class="use-btn" @click="viewDetail" :class="[`type-4`]">查看详情</div> -->
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { useMarkdown } from "../hooks/markdown";

const props = defineProps({
  anwser: {
    type: Object,
  },

  chatStatus: {
    type: Object,
  },
});
const { marked } = useMarkdown();
function viewQuote() {
  emits("showQuote", getQuoteList.value);
}
function viewCtx() {
  // emits("showCtx");
}
function viewDetail() {
  emits("showDetail");
}
const textVal = ref("");
watchEffect(async () => {
  textVal.value = marked(props.anwser.value[0].text.content ?? "");
});
const getQuoteList = computed(() => {
  console.log("props.answer", props.anwser);

  const uniqueArray = props.anwser.totalQuoteList.reduce(
    (acc, current) => {
      const key = current.sourceId;
      if (!acc.seen.has(key)) {
        acc.seen.set(key, true);
        acc.result.push(current);
      }
      return acc;
    },
    { seen: new Map(), result: [] }
  ).result;
  console.log("uniqueArray", uniqueArray);
  return uniqueArray;
});

const emits = defineEmits(["showCtx", "showDetail", "copy", "approveFeedback", "showQuote", "showCollectQuote"]);
</script>

<style scoped lang="scss">
.anwser {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  color: #282828;
  font-size: 16px;
  .feed-back-btn {
    background-color: #12b76a;

    > i {
      color: #fff;
    }
  }
  .un-feed-back-btn {
    background-color: #fc9663;

    > i {
      color: #fff;
    }
  }
  .status-box {
    display: flex;
    align-items: center;
    height: 24px;
    padding: 0 10px;
    border-radius: 4px;
    font-size: 12px;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 5px;
      animation: opacity 1s linear infinite;
    }
    @keyframes opacity {
      0% {
        opacity: 0.4;
      }
      100% {
        transform: 1;
      }
    }
  }
  .anwser-body {
    background-color: #f7f8fa;
    border-radius: 0 8px 8px 8px;
    text-align: left;
    word-wrap: break-word;
    padding: 12px;
    margin-top: 10px;
    .mark-info {
      max-width: 100%;
    }
    .use-sign {
      margin-top: 10px;
      > i {
        color: rgb(232, 47, 114);
      }
      > i + span {
        color: #668075;
        font-size: 13px;
      }
      .line {
        flex: 1;
        height: 1px;
        background-color: #e5e5e5;
        margin: 0 10px;
      }
    }
    .file-use {
      width: fit-content;
      .file-use-item {
        border: 1px solid #e8ebf0;
        border-radius: 6px;
        // padding: 10px;
        margin: 10px 0;
        height: 22px;
        cursor: pointer;
        .idx {
          width: 16px;
          height: 100%;
          background-color: #f0f1f6;
          font-size: 12px;
        }
        .file-info {
          padding: 0 7px;
          column-gap: 5px;
          > i {
            color: rgb(69, 176, 88);
          }
          .file-name {
            font-size: 12px;
          }
        }
      }
    }
    .quote-wrapper {
      flex-wrap: wrap;
      gap: 5px;
    }
    .other-info {
      display: flex;
      column-gap: 10px;
      .use-btn {
        cursor: pointer;
        display: flex;
        height: 18px;
        align-items: center;
        padding: 0 4px;
        font-size: 12px;
        border-radius: 4px;
      }
    }
  }
  $colors: #3370ff, #1fcc7e, #a558c9, #767e92;
  @for $i from 1 through length($colors) {
    .use-btn.type-#{$i} {
      $color: nth($colors, $i);
      background-color: lighten($color, 60%);
      color: $color;
      border: solid 1px $color;
    }
  }
  $statusColors: #767e92, #71d4a8;
  $status: ("ready", "running");
  @for $i from 1 through length($statusColors) {
    .status-type-#{nth($status, $i)} {
      content: ($i);
      $color: nth($statusColors, $i);
      background-color: lighten($color, 30%);
      // border: solid 1px $color1;
      .dot {
        background-color: $color;
      }
    }
  }
}
</style>
<style lang="scss">
@import "./index.module.scss";
// .bread-div {
//   padding: 0.5rem;
//   border-bottom: 1px solid #eee;
//   background-color: #e1f0ff;
// }
// .detailed-title {
//   font-size: 1.8rem;
//   text-align: center;
//   padding: 1rem;
// }
// .center {
//   text-align: center;
// }
// .detailed-content {
//   padding: 1.3rem;
//   font-size: 1rem;
// }
// pre {
//   display: block;
//   background-color: #f3f3f3;
//   padding: 0.5rem !important;
//   overflow-y: auto;
//   font-weight: 300;
//   font-family: Menlo, monospace;
//   border-radius: 0.3rem;
// }
// pre {
//   background-color: #283646 !important;
// }
// pre > code {
//   border: 0px !important;
//   background-color: #283646 !important;
//   color: #fff;
//   line-height: 20px;
// }
// code {
//   display: inline-block;
//   background-color: #f3f3f3;
//   border: 1px solid #fdb9cc;
//   border-radius: 3px;
//   font-size: 12px;
//   padding-left: 5px;
//   padding-right: 5px;
//   color: #4f4f4f;
//   margin: 0px 3px;
// }

// .title-anchor {
//   color: #888 !important;
//   padding: 4px !important;
//   margin: 0rem !important;
//   height: auto !important;
//   line-height: 1.2rem !important;
//   font-size: 0.7rem !important;
//   border-bottom: 1px dashed #eee;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }
// .active {
//   color: rgb(30, 144, 255) !important;
// }
// .nav-title {
//   text-align: center;
//   color: #888;
//   border-bottom: 1px solid rgb(30, 144, 255);
// }
// .article-menu {
//   font-size: 12px;
// }
// iframe {
//   height: 34rem;
// }
// .detailed-content img {
//   width: 100%;
//   border: 1px solid #f3f3f3;
// }
// .title-level3 {
//   display: none !important;
// }
// .ant-anchor-link-title {
//   font-size: 12px !important;
// }
// .ant-anchor-wrapper {
//   padding: 5px !important;
// }
</style>

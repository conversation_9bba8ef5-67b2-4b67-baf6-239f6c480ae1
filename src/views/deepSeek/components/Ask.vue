<template>
  <div class="ask">
    <div class="chat-op-box flex-row">
      <div class="op-box flex-row">
        <div class="op-item flex-center" @click="emits('copy', askInfo.value[0].text.content)">
          <i class="iconfont sw-fuzhi"></i>
        </div>
        <div class="op-item flex-center" @click="emits('reAsk')">
          <i class="iconfont sw-shuaxin"></i>
        </div>
        <div class="op-item flex-center" @click="emits('deleteMessage')">
          <i class="iconfont sw-shanchu"></i>
        </div>
      </div>
      <div class="avatar flex-center">
        <div class="circle flex-center">
          <i class="iconfont sw-touxiang"></i>
        </div>
      </div>
    </div>
    <div class="ask-body Markdown_markdown__h2C_t">
      <!-- <p>{{ askInfo.value[0].text.content }}</p> -->
      <div class="quote-content markdown" v-html="textVal"></div>
    </div>
  </div>
</template>

<script setup>
import { useMarkdown } from "../hooks/markdown";
const props = defineProps({
  askInfo: {
    type: Object,
  },
});
const { marked } = useMarkdown();
const textVal = computed(() => {
  return marked(props.askInfo.value[0].text.content ?? "");
});
const emits = defineEmits(["copy", "reAsk", "deleteMessage"]);
</script>

<style scoped lang="scss">
@import "@/assets/styles/variables.module.scss";
.ask {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .ask-body {
    position: relative;
    margin-top: 10px;
    min-width: 0px;
    word-wrap: break-word;
    padding: 12px;
    background-color: #e1eaff;
    border-radius: 8px 0 8px 8px;
    font-size: 13px;
  }
}
</style>

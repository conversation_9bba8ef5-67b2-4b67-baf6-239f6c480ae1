<template>
  <div class="side-bar collapse-bar" v-loading="sidebarInfo.loading" :class="{ 'collapse-side': sidebarInfo.collapse }">
    <div class="header-logo" v-if="!sidebarInfo.collapse">
      <img style="zoom: 0.08" src="@/assets/images/logo.png" alt="" />
      <span class="logo-name">AI助手</span>
    </div>
    <div class="n-chat-box flex-between" v-show="!sidebarInfo.collapse">
      <div class="btn flex-center" @click="emits('chooseHisChat', null)">
        <i class="iconfont sw-dangqianhuihua"></i>
        <span>新对话</span>
      </div>
      <div class="circle-btn flex-center" @click="clearAllHisList">
        <i class="iconfont sw-qingchu"></i>
      </div>
    </div>
    <el-scrollbar ref="scrollRef" @scroll="handleScroll" style="height: calc(100% - 125px)">
      <div class="chat-item-list" ref="wrapRef">
        <div class="chat-item flex-row" v-for="(item, index) in chatHisList" :key="item.chatId" :class="{ 'active-item': chatInfo.chatId === item.chatId, 'top-chat-item': item.top }" @click="chooseChatHis(item)">
          <i :class="['iconfont', chatInfo.chatId === item.chatId ? 'sw-duihua' : 'sw-dangqianhuihua']"></i>
          <div :title="item.customTitle || item.title" class="name">{{ item.customTitle || item.title }}</div>
          <div class="time">{{ getSubStrDate(item.updateTime) }}</div>
          <el-popover popper-class="sidebar-popover" v-if="item.popVisible" :show-arrow="false" placement="bottom-end" trigger="hover" :transition="false" @before-leave="hidePopover(item)">
            <template #reference>
              <div class="more-rect flex-center">
                <i class="iconfont sw-more"></i>
              </div>
            </template>
            <!-- 内容模板 -->
            <div class="op-box">
              <div class="op-item" @click="op.action(item)" v-for="(op, idx) in opList" :key="idx">
                <i :class="['iconfont', op.icon]"></i>
                <span>{{ op.name(item) }}</span>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
      <div v-if="hisLoading" class="loading-text">加载中...</div>
    </el-scrollbar>
    <div class="collapse-btn" @click="collapseEvent()">
      <i class="iconfont sw-zhedie" :class="{ 'isCollapse-icon': sidebarInfo.collapse }"></i>
    </div>
    <el-dialog class="custom-title-dialog chat-el-dialog" v-model="editTitleVisible" width="400" top="30vh" style="margin-top: 15vh" align-center>
      <template #header>
        <div class="flex-row ctx-dialog-header">
          <i class="iconfont sw-bianji"></i>
          <div>重命名</div>
        </div>
      </template>
      <div class="title-input">
        <el-input style="width: 300px" v-model="newTitle" placeholder="请输入标题" clearable></el-input>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editTitleVisible = false">取消</el-button>
          <el-button type="primary" @click="sure">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { getChatHisList, removeAllHisList, updateHisChatInfo, removeChatHis } from "@/api/chat/fastGpt";
// import { debounce } from "@/utils/index";
import { formatTimeToChatTime } from "../lib/utils";
import dayjs from "dayjs";

const props = defineProps({
  chatInfo: Object,
  sidebarInfo: Object,
});
const _chatHisInfo = ref(null);
const chatHisList = ref([]);
const newTitle = ref("");
const editTitleVisible = ref(false);
const opList = [
  {
    name: (item) => {
      return item.top ? "取消置顶" : "置顶";
    },
    icon: "sw-zhiding-weizhiding",
    action: (item) => {
      _chatHisInfo.value = item;
      newTitle.value = item.title;
      _chatHisInfo.value.top = !item.top;
      sure(null, true);
    },
  },
  {
    name: () => "重命名",
    icon: "sw-bianjizidingyi",
    action: (item) => {
      _chatHisInfo.value = item;
      newTitle.value = item.title;
      editTitleVisible.value = true;
    },
  },
  {
    name: () => "删除",
    icon: "sw-shanchu",
    action: (item) => {
      console.log("item", item);
      const { chatId, appId } = item;
      if (appId) {
        props.sidebarInfo.loading = true;
        removeChatHis({ appId, chatId })
          .then((res) => {
            if (res.code === 200) {
              ElMessage({ message: "删除成功", type: "success" });
              resetRemoveFeedback(chatId);
            }
          })
          .finally(() => {
            props.sidebarInfo.loading = false;
          });
      } else {
        resetRemoveFeedback(chatId);
      }
    },
  },
];

const getSubStrDate = computed(() => {
  return (str) => {
    // dayjs(str).format("YYYY-MM-DD HH:mm");
    return formatTimeToChatTime(str);
  };
});
const chooseChatHis = (item) => {
  if (props.chatInfo.chatId === item.chatId) return;
  props.chatInfo.chatId = item.chatId;
  emits("chooseHisChat", item);
};
function resetRemoveFeedback(chatId) {
  const idx = chatHisList.value.findIndex((_) => _.chatId === chatId);
  chatHisList.value.splice(idx, 1);
  if (chatId === props.chatInfo.chatId) {
    props.chatInfo.chatId = "";
    emits("chooseHisChat", null);
  }
}

function sure(_e, setTop) {
  if (!newTitle.value) {
    return ElMessage({ message: "请输入标题", type: "warning" });
  }
  props.sidebarInfo.loading = true;
  const { appId } = props.chatInfo;
  const chatId = _chatHisInfo.value.chatId;
  const request = setTop ? { chatId, appId, top: _chatHisInfo.value.top } : { chatId, appId, customTitle: newTitle.value };
  updateHisChatInfo(request)
    .then((res) => {
      if (res.code === 200) {
        if (!setTop) {
          ElMessage({ message: "修改成功", type: "success" });
        }
        props.sidebarInfo.loading = true;
        _chatHisInfo.value.title = newTitle.value;
        editTitleVisible.value = false;
        if (setTop && _chatHisInfo.value.top) {
          //查找列表中最后一个top为true的元素，
          // 将chatId等于chatInfo.chatId的元素移动到最后一个top为true的元素后面且如果没有top为true的元素，
          // 就移动到第一个元素前面
          const lastTop = chatHisList.value.find((_) => _.top && _.chatId !== _chatHisInfo.value.chatId);
          const index = chatHisList.value.findIndex((_) => _.chatId === _chatHisInfo.value.chatId);
          if (lastTop) {
            const item = chatHisList.value.splice(index, 1)[0];
            item.top = _chatHisInfo.value.top;
            chatHisList.value.splice(chatHisList.value.indexOf(lastTop) + 1, 0, item);
          } else {
            const item = chatHisList.value.splice(index, 1)[0];
            item.top = _chatHisInfo.value.top;
            chatHisList.value.unshift(item);
          }
        } else {
          //查找列表中第一个top为false的元素，
          // 将chatId等于chatInfo.chatId的元素移动到第一个top为false的元素前面且如果没有top为false的元素，
          // 就移动到最后一个元素后面
          const firstTop = chatHisList.value.find((_) => !_.top && _.chatId !== _chatHisInfo.value.chatId);
          const index = chatHisList.value.findIndex((_) => _.chatId === _chatHisInfo.value.chatId);
          if (firstTop) {
            const item = chatHisList.value.splice(index, 1)[0];
            item.top = _chatHisInfo.value.top;
            chatHisList.value.splice(chatHisList.value.indexOf(firstTop), 0, item);
          } else {
            const item = chatHisList.value.splice(index, 1)[0];
            item.top = _chatHisInfo.value.top;
            chatHisList.value.push(item);
          }
        }
      }
    })
    .finally(() => {
      props.sidebarInfo.loading = false;
    });
}

//解决element-plus在v-for中的闪屏问题
function hidePopover(item) {
  item.popVisible = false;
  const timer = setTimeout(() => {
    item.popVisible = true;
    clearTimeout(timer);
  }, 0);
}
function collapseEvent() {
  props.sidebarInfo.collapse = !props.sidebarInfo.collapse;
}

function clearAllHisList() {
  removeAllHisList({ appId: props.chatInfo.appId }).then((res) => {
    if (res.code === 200) {
      ElMessage({
        message: "清除成功",
        type: "success",
      });
      chatHisList.value = [];
      currentIdx.value = "";
      emits("chooseHisChat", null);
    }
  });
}
function updataChatHis(chatId, name) {
  const hasChat = chatHisList.value.find((_) => _.chatId === chatId);
  if (!hasChat) {
    const customTitle = hasChat?.customTitle;
    const data = { chatId: chatId, customTitle: customTitle || "", title: customTitle || name, updateTime: new Date().toLocaleString() };
    chatHisList.value.unshift({
      ...data,
      popVisible: true,
    });
  } else {
    hasChat.name = name;
  }
}
const hisQuery = reactive({
  pageSize: 4,
  offset: 1,
  total: 0,
});
function getChatHistoryList() {
  getChatHisList({ ...hisQuery, source: "api" })
    .then((res) => {
      if (res.code === 200) {
        chatHisList.value = [...chatHisList.value, ...res.data.list].map((_) => {
          _.popVisible = true;
          return _;
        });
        hisQuery.total = res.data.total;
        hisLoading.value = false;
      }
    })
    .finally(() => {
      props.sidebarInfo.loading = false;
    });
}
const hisLoading = ref(false);
const scrollRef = ref(null);
const wrapRef = ref(null);
function handleScroll({ scrollTop }) {
  const clientHeight = scrollRef.value.$el.clientHeight;
  const wraHeight = wrapRef.value.offsetHeight;
  if (scrollTop + clientHeight >= wraHeight) {
    if (hisQuery.offset * hisQuery.pageSize < hisQuery.total) {
      hisLoading.value = true;
      hisLoading.value = true;
      hisQuery.offset += 1;
      getChatHistoryList();
    }
  }
}

getChatHistoryList();
defineExpose({ updataChatHis });
const emits = defineEmits(["chooseHisChat"]);
</script>

<style scoped lang="scss">
@import "@/assets/styles/variables.module.scss";
.side-bar {
  width: 290px;
  font-size: 13px;
  flex: none;
  transition: 0.3s all;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
  position: relative;
  // border-right: solid 1px #e5e5e5;
  box-shadow: 1px 0 0 0 #e5e5e5;
  .header-logo {
    height: 60px;
    padding: 20px 20px 8px 20px;
    display: flex;
    align-items: center;
    column-gap: 10px;
    .logo-name {
      overflow: hidden;
      /* 超出部分隐藏 */
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .n-chat-box {
    width: 100%;
    height: 36px;
    padding: 0 20px;
    column-gap: 10px;
    margin: 15px 0;
    .btn {
      flex: 1;
      height: 34px;
      border-radius: 16px;
      border: solid 1px #e5e5e5;
      color: $--color-primary;
      column-gap: 5px;
      cursor: pointer;
      font-size: 14px;
      &:hover {
        background-color: rgba(51, 112, 255, 0.1);
        border-color: $--color-primary;
      }
    }
    .btn + div {
      width: 34px;
      height: 34px;
      cursor: pointer;
      border-radius: 50%;
      border: solid 1px #e5e5e5;
      &:hover {
        border-color: $pink;
        color: $pink;
      }
    }
  }
  .chat-item-list {
    padding: 0 20px;
    .chat-item {
      height: 44px;
      margin-bottom: 8px;
      padding: 0 16px;
      border-radius: 8px;
      cursor: pointer;
      color: #485264;
      .name {
        flex: 1 0 0;
        margin-left: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .time {
        display: block;
      }
      .more-rect {
        width: 20px;
        height: 18px;
        border: solid 1px #e5e5e5;
        border-radius: 4px;
        display: none;
        &:hover {
          color: $--color-primary;
        }
      }
      &:hover {
        background-color: #f7f8fa;
        .more-rect {
          display: block;
        }
        .time {
          display: none;
        }
      }
    }
    .top-chat-item {
      background-color: #e6f6f6;
      &:hover {
        background-color: #e6f6f6;
      }
    }
    .active-item {
      background-color: #f0f4ff;
      > i,
      > i + div {
        color: $--color-primary;
      }
      &:hover {
        background-color: #f0f4ff;
      }
    }
  }
  .loading-text {
    padding-left: 35px;
    color: $--color-primary;
  }
  .collapse-btn {
    position: absolute;
    right: -14px;
    z-index: 1;
    top: 50%;
    width: 0;
    height: 60px;
    transform: translate(0, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: 15px solid #ebebeb;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    cursor: pointer;

    i {
      color: #b3b3b3;
      font-size: 12px;
      margin-left: -16px;
    }

    &:active {
      border-left: 12px solid #eff6fe;
    }

    &:hover {
      i {
        // color: var(--PrimaryColor);
      }
    }

    .isCollapse-icon {
      transform: rotate(180deg);
    }
  }
}
.collapse-side {
  width: 0px;
  transition: 0.3s all;
  border-right: none;
}

// .sidebar-transform-enter-active,
// .sidebar-transform-leave-active {
//   transition: width 0.5s ease;
// }

// .sidebar-transform-enter-from,
// .sidebar-transform-leave-to {
//   width: 290px;
// }
</style>
<style lang="scss">
@import "@/assets/styles/variables.module.scss";
.sidebar-popover.el-popper {
  padding: 4px;
  .op-box {
    .op-item {
      height: 36px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      column-gap: 10px;
      padding: 0 10px;
      cursor: pointer;
      &:hover {
        background-color: #f0f4ff;
        color: $--color-primary;
        &:last-child {
          background-color: rgba(217, 45, 32, 0.1);
        }
      }
      &:last-child {
        color: #d92d20;
      }
    }
  }
}

.custom-title-dialog {
  padding: 0;
  border-radius: 8px;
  .el-dialog__header {
    padding: 0 !important;
    margin-right: 0;
  }

  .title-input {
    padding: 30px 0 10px 0;
    // height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
</style>

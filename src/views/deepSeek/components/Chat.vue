<template>
  <div class="seek-chat" v-loading="loading">
    <div :style="{ '--qWidth': quote.show ? '560px' : '0px' }" class="main-chat-content">
      <div class="chat-header">
        <div class="name">{{ getHeadTitle }}</div>
        <div class="record-count flex-center">
          <i class="iconfont sw-jilu"></i>
          {{ chatList.length }}条记录
        </div>
      </div>
      <div class="chat-content-info" ref="scrollRef">
        <!-- <el-scrollbar ref="scrollRef"> -->
        <div class="scrollbar">
          <div class="chat-list">
            <div class="chat-item" v-for="(item, index) in chatList" :key="index">
              <ask v-if="item.obj === CHAT_ROLE_TYPE.HUMAN" @copy="openCopyDialog" @reAsk="deleteMessage(item, true)" @deleteMessage="deleteMessage(item)" :askInfo="item"></ask>
              <anwser v-else :chatStatus="chatStatus" :anwser="item" :flowNodeResponse="item.flowNodeResponse" @showQuote="showQuote($event, item)" @showCtx="openCtxDialog(item)" @showDetail="openDetailDialog(item)" @copy="openCopyDialog" @showCollectQuote="showCollectQuote($event, item)" @approveFeedback="approveFeedback(item)" @unApproveFeedback="unApproveFeedback(item)"></anwser>
            </div>
          </div>
        </div>
      </div>
      <div class="chat-bottom">
        <div class="ask-box">
          <el-input ref="inputRef" @keydown="handleKeydown" v-model="askContent" class="ask-input" autosize :resize="0" type="textarea" placeholder="有问题，尽管问,发送[shift+enter]换行" />
          <div v-if="chatStatus.code === CHAT_STATUS.READY" @click="chatStart" title="发送" class="send-btn flex-center">
            <i class="iconfont sw-fasong"></i>
          </div>
          <div v-else class="send-btn flex-center pause-btn" @click="abortChat">
            <i class="iconfont sw-zanting"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="reponse-quote" v-if="quote.show">
      <collect-quote :appId="chatInfo.appId" :dataId="quote.dataId" :chatId="chatInfo.chatId" :totalQuoteList="quote.totalQuoteList" v-if="quote.type === 'collect'" @close="quote.show = false"></collect-quote>
      <quote :appId="chatInfo.appId" :dataId="quote.dataId" :chatId="chatInfo.chatId" :totalQuoteList="quote.totalQuoteList" v-else @close="quote.show = false"></quote>
    </div>
    <el-dialog class="ctx-dialog-box chat-dialog chat-el-dialog" v-model="dialogVisible.showCtx" width="600" align-center>
      <template #header>
        <div class="flex-row ctx-dialog-header">
          <img style="width: 20px; height: 20px" src="@/assets/images/chatHistory.svg" alt="" />
          <div>上下文预览5条</div>
        </div>
      </template>
      <el-scrollbar max-height="600px">
        <div class="ctx-dialog-content">
          <div class="ctx-item" v-for="(item, index) in ctxList" :key="index">
            <div class="name">{{ item.obj }}</div>
            <div>{{ item.value }}</div>
          </div>
        </div>
      </el-scrollbar>
    </el-dialog>
    <el-dialog class="ctx-dialog-box chat-dialog chat-el-dialog" v-model="dialogVisible.showDetail" width="900" align-center>
      <template #header>
        <div class="flex-row ctx-dialog-header">
          <i class="iconfont sw-pingjunxiangyingshijian"></i>
          <div>完整响应</div>
        </div>
      </template>

      <div class="ctx-dialog-content ctx-dialog-content-detail" style="height: 60vh">
        <div class="detail-navbar">
          <div
            class="bar-item"
            v-for="(item, index) in detailInfo.list"
            :key="index"
            @click="chooseDetail(item)"
            :class="{
              'active-detail-item': currentDetail.moduleName === item.moduleName,
            }"
            :style="{ '--iColor': [detailIconList[index].color] }"
          >
            <div class="nav-img">
              <i class="iconfont" :class="[detailIconList[index].icon]"></i>
            </div>
            <div>
              {{ item.moduleName }}
            </div>
          </div>
        </div>
        <div class="detail-wrapper">
          <el-scrollbar>
            <div class="row-info" v-for="(item, index) in detailNodeList" :key="index">
              <div class="node-name">{{ item.name }}</div>
              <div class="node-value">
                <div v-if="item.name === '问题优化输入/输出Tokens'">{{ currentDetail.queryExtensionResult.inputTokens }}/{{ currentDetail.queryExtensionResult.outputTokens }}</div>
                <div v-else-if="item.value">{{ item.value }}</div>
                <div v-else-if="item.subKey">
                  {{ currentDetail[item.key][item.subKey] }}
                </div>
                <div v-else-if="item.isHtml" v-html="marked(currentDetail.queryExtensionResult.query)"></div>
                <div v-else>{{ currentDetail[item.key] }}</div>
              </div>
            </div>
            <template v-if="currentDetail.moduleName === 'AI 对话'">
              <div class="detail-ctx-content">
                <div class="detail-ctx-item" v-for="(item, index) in currentDetail.historyPreview" :key="index">
                  <div class="name">{{ item.obj }}</div>
                  <div>{{ item.value }}</div>
                </div>
              </div>
            </template>
            <template v-if="currentDetail.moduleName === '知识库搜索'">
              引用内容:
              <div class="detail-quote-wrapper" v-for="(q, index) in detailInfo.quoteList" :key="index">
                <div class="file-use">
                  <div class="rankbox">{{ index + 1 }}# | 综合排名</div>
                  <!-- {{ quoteList[index].q }} -->
                  <div class="mark-value markdown Markdown_markdown__h2C_t" v-html="getTextVal(detailInfo.responseQuoteList[index]?.q)"></div>
                  <div class="file-use-item flex-row">
                    <div class="file-info flex-row">
                      <i style="color: rgb(69, 176, 88)" class="iconfont sw-wendang"></i>
                      <div class="file-name">{{ q.sourceName }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-scrollbar>

          <!-- <div class="ctx-item" v-for="(item, index) in ctxList" :key="index">
            <div class="name">{{ item.obj }}</div>
            <div>{{ item.value }}</div>
          </div> -->
        </div>
      </div>
    </el-dialog>
    <el-dialog class="copy-value-dialog chat-el-dialog" v-model="dialogVisible.copyValue" width="400" top="30vh" style="margin-top: 15vh" align-center>
      <template #header>
        <div class="flex-row ctx-dialog-header">
          <i class="iconfont sw-fuzhi"></i>
          <div>复制</div>
        </div>
      </template>
      <div class="title-input">
        <div class="notice">无法使用浏览器自动复制，请手动复制下面内容</div>
        <el-scrollbar max-height="300px">
          <div class="value-content">
            <div v-html="copyValueTxt"></div>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>
    <el-dialog class="copy-value-dialog chat-el-dialog" v-model="dialogVisible.showDisapproval" width="400" top="30vh" style="margin-top: 15vh" align-center>
      <template #header>
        <div class="flex-row ctx-dialog-header">
          <i style="color: rgb(22, 196, 175)" class="iconfont sw-caizan"></i>
          <div>结果反馈</div>
        </div>
      </template>
      <div class="title-input">
        <el-input style="margin-top: 20px" v-model="disApproveContent" :resize="0" :rows="5" type="textarea" @keyup.enter="chatStart" placeholder="输入你觉得回答不满意的地方" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.showDisapproval = false">关闭</el-button>
          <el-button type="primary" @click="submitDisApprove">提交反馈</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import Ask from "./Ask.vue";
import Anwser from "./Anwser.vue";
import Quote from "./Quote.vue";
import CollectQuote from "./CollectQuote.vue";
import { startNodes, searchNodes, aiNodes, detailIconList } from "../lib/reponse";
import { useMarkdown } from "../hooks/markdown";
import { approvalFeedback, getHisChatMessageList, viewDat, getQuote, getChatDetail, deleteHisChatMessage } from "@/api/chat/fastGpt";
import { useChatMessage, CHAT_ROLE_TYPE, CHAT_STATUS } from "../hooks/chat";
import { useInput } from "../hooks/input";
const props = defineProps({
  chatInfo: {
    type: Object,
  },
});

const emits = defineEmits(["showQute", "updateChatHis"]);
const { scrollRef, askContent, chatList, chatStatus, quote, showQuote, showCollectQuote, chatStart, abortChat, scrollToBottom } = useChatMessage(props, emits);
const { inputRef, handleKeydown } = useInput(askContent, chatStart);
const { marked, formatMarkTable } = useMarkdown();
const loading = ref(false);
const getHeadTitle = computed(() => {
  return props.chatInfo.chatTitle ?? "新对话";
});
watch(
  () => props.chatInfo.chatId,
  (val) => {
    if (!val) return;
  },
  { immediate: true, deep: true }
);
//以下为弹出列表具体操作
const dialogVisible = reactive({
  showCtx: false,
  showDetail: false,
  copyValue: false,
  showDisapproval: false,
});
const ctxList = ref([]);
async function openCtxDialog(chat) {
  console.log("chat", chat);
  dialogVisible.showCtx = true;
  if (!chat.responseNodeList) {
    await getChatResData(chat);
  }
  ctxList.value = chat?.responseNodeList.find((_) => _.historyPreview && _.historyPreview.length).historyPreview;
  // ctxList.value = chat.historyPreviewList??[];
}
const detailInfo = reactive({
  list: [],
  quoteList: [],
  dataId: "",
  responseQuoteList: [],
});
const detailNodeList = ref([]);
async function openDetailDialog(chat) {
  dialogVisible.showDetail = true;
  if (!chat.responseNodeList) {
    await getChatResData(chat);
  }
  detailInfo.list = chat?.responseNodeList || [];
  detailInfo.quoteList = chat?.totalQuoteList || [];
  detailInfo.dataId = chat?.dataId || "";
  chooseDetail(detailInfo.list[0]);
}

const currentDetail = ref({});

function chooseDetail(item) {
  if (item.moduleName === "流程开始") {
    detailNodeList.value = startNodes;
  } else if (item.moduleName === "知识库搜索") {
    detailNodeList.value = searchNodes;
    //接口知识库搜索
    const request = {
      appId: props.chatInfo.appId,
      chatId: props.chatInfo.chatId,
      chatItemDataId: detailInfo.dataId,
      collectionIdList: [...new Set(detailInfo.quoteList.map((_) => _.collectionId))],
      datasetDataIdList: detailInfo.quoteList.map((_) => _.id),
    };
    getQuote(request).then((res) => {
      // console.log("res-----+++++++++++++++++++", res);
      detailInfo.responseQuoteList = res.data;
      // quoteList.value = res.data;
    });
  } else {
    detailNodeList.value = aiNodes;
  }
  currentDetail.value = item;
}

const getTextVal = computed(() => {
  return (item) => {
    let value = item ?? "";
    value = formatMarkTable(value);

    return marked(value);
  };
});

const copyValueTxt = ref("");
function openCopyDialog(text) {
  copyValueTxt.value = marked(text);
  dialogVisible.copyValue = true;
}
const disApproveContent = ref("");
function approveFeedback(item) {
  const request = {
    appId: props.chatInfo.appId,
    chatId: props.chatInfo.chatId,
    dataId: item.dataId,
    userGoodFeedback: !item.userGoodFeedback ? "yes" : undefined,
  };
  loading.value = true;
  approvalFeedback(request)
    .then((res) => {
      item.userGoodFeedback = !item.userGoodFeedback;
    })
    .finally(() => {
      loading.value = false;
    });
}
let currentChat = null;
function unApproveFeedback(item) {
  currentChat = item;
  if (currentChat.userBadFeedback) {
    submitDisApprove(null, true);
  } else {
    dialogVisible.showDisapproval = true;
    disApproveContent.value = "";
  }
}
function submitDisApprove($e, status = false) {
  const request = {
    appId: props.chatInfo.appId,
    chatId: props.chatInfo.chatId,
    dataId: currentChat.dataId,
    userBadFeedback: status ? undefined : disApproveContent.value,
  };
  loading.value = true;
  approvalFeedback(request)
    .then((res) => {
      if (status) {
        currentChat.userBadFeedback = "";
      } else {
        currentChat.userBadFeedback = disApproveContent.value;
        dialogVisible.showDisapproval = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

async function getChatResData(chat) {
  const result = await getChatDetail({ chatId: props.chatInfo.chatId, appId: props.chatInfo.appId, dataId: chat.dataId });
  chat.responseNodeList = result.data;
}

//接口处理
function deleteMessage(chat, reAsk = false) {
  console.log("chat", chat);
  if (reAsk) {
    askContent.value = chat.value[0].text.content;
  }
  const idx = chatList.value.findIndex((_) => _.dataId === chat.dataId);
  loading.value = true;
  const request = {
    appId: props.chatInfo.appId,
    chatId: props.chatInfo.chatId,
    contentId: chat.dataId,
  };
  const aiAnswerContentId = chatList.value.length > idx + 1 ? chatList.value[idx + 1].dataId : "";
  if (aiAnswerContentId) {
    Promise.any([(deleteHisChatMessage(request), deleteHisChatMessage({ ...request, contentId: aiAnswerContentId }))])
      .then((res) => {
        chatList.value.splice(idx, 2);
        reAsk && chatStart();
      })
      .finally(() => {
        loading.value = false;
      });
  }
}
function getHisChatMessageListByChatId(params) {
  loading.value = true;
  const { chatId, appId } = props.chatInfo;
  quote.show = false;
  getHisChatMessageList({ chatId, appId }).then((res) => {
    console.log("res", res);
    chatList.value = res.data.list;
    loading.value = false;
    nextTick(() => {
      scrollToBottom();
    });
    // scrollToBottom();
  });
}

function exposeChatStart(content) {
  askContent.value = content;
  chatStart();
}
defineExpose({ exposeChatStart, getHisChatMessageListByChatId });
</script>

<style lang="scss">
@import "@/assets/styles/variables.module.scss";
.seek-chat {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #f4f4f7;
  // flex-direction: column;

  .main-chat-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #fff;
    width: calc(100% - var(--qWidth));
    .chat-header {
      height: 60px;
      display: flex;
      align-items: center;
      padding-left: 15px;
      background-color: rgb(251, 251, 252);
      border-bottom: solid 1px rgb(226, 232, 240);
      border-top-right-radius: 16px;
      column-gap: 10px;
      .name {
        font-size: 14px;
      }
      .record-count {
        color: $--color-primary;
        background-color: #f0f4ff;
        padding: 0 10px;
        height: 24px;
        border-radius: 4px;
        font-size: 12px;
        column-gap: 10px;
      }
    }
  }
  .reponse-quote {
    width: 560px;
    margin-left: 15px;
    border-radius: 16px;
    background-color: #fff;
    border: solid 1px rgb(226, 232, 240);
  }

  .chat-content-info {
    flex: 1 0 0;

    padding-top: 20px;
    overflow: overlay;
    .scrollbar {
      margin: 0 50px;
      height: 100%;
    }
    // border: solid 1px red;
    .chat-list {
      width: 100%;
      .chat-item {
        width: 100%;
      }
    }
  }
  .ai-intro-box {
    margin: 10px auto;
    max-width: min(800px, 100%);
    width: 100%;
    column-gap: 20px;
    .text-intro {
      display: flex;
      flex-direction: column;
      row-gap: 5px;
    }
    .name {
      font-size: 20px;
      font-weight: 600;
    }
    .func {
      font-size: 14px;
    }
  }

  .chat-bottom {
    margin: 10px auto;
    max-width: min(800px, 100%);
    width: 100%;
    padding: 0 20px;
    .ask-box {
      // padding-top: 18px;
      // padding-bottom: 18px;
      padding: 18px;
      border-radius: 4px;
      width: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(0, 0, 0, 0.12);
      display: flex;
      align-items: flex-end;

      .send-btn {
        height: 32px;
        width: 32px;
        border-radius: 4px;
        background-color: $--color-primary;
        color: #fff;
        cursor: pointer;
      }
      .pause-btn {
        animation: scale 1s linear infinite;
      }
      @keyframes scale {
        0% {
          transform: scale(1);
        }
        100% {
          transform: scale(1.2);
        }
      }
    }
  }
}
.chat-op-box {
  display: flex;
  column-gap: 10px;
  .op-box {
    border-radius: 6px;
    border: solid 1px #e5e5e5;
    .op-item {
      color: rgb(138, 169, 147);
      width: 24px;
      height: 24px;
      cursor: pointer;
      border-right: solid 1px #e5e5e5;
      > i {
        font-size: 12px;
      }

      &:hover {
        color: #1fcc7e;
      }
      &:last-child {
        border-right: none;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
    }
  }

  .avatar {
    width: 34px;
    height: 34px;
    border: solid 1px #e5e5e5;
    border-radius: 6px;
    .circle {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: $--color-primary;
      color: #fff;
    }
  }
}
.ask-input {
  textarea {
    outline: none;
    // border: 0;
    width: 100%;
    resize: none;
    box-shadow: unset !important;
    font-size: 13px;
    overflow: hidden;
    min-height: 50px;
  }
}
.chat-dialog {
  margin-top: 15vh;
}
.ctx-dialog-box {
  .ctx-dialog-content {
    padding: 20px 20px;
    .ctx-item {
      border-radius: 8px;
      margin-bottom: 10px;
      padding: 10px;
      border: solid 1px #e5e5e5;
      .name {
        color: #333;
        font-weight: 600;
        margin-bottom: 10px;
      }
      &:nth-of-type(2n) {
        background-color: #fbfbfc;
      }
    }

    .detail-navbar {
      width: 270px;
      height: 100%;
      border-right: solid 1px #e5e5e5;
      .bar-item {
        height: 50px;
        display: flex;
        align-items: center;
        cursor: pointer;
        border-radius: 8px;
        padding-left: 10px;
        column-gap: 10px;
        &:hover {
          background-color: #f4f4f7;
        }
        .nav-img {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 24px;
          height: 24px;
          border-radius: 4px;
          background-color: var(--iColor);
          > i {
            color: #fff;
          }
        }
      }
      .active-detail-item {
        background-color: #f4f4f7;
      }
    }
  }
  .ctx-dialog-content-detail {
    padding: 20px 0 0 20px;
    display: flex;
    .detail-wrapper {
      padding: 10px;
      flex: 1;
      .row-info {
        margin-bottom: 10px;
        .node-name {
          font-weight: 500;
          color: #333;
        }
        .node-value {
          width: 570px;
          min-height: 40px;
          display: flex;
          align-items: center;
          padding: 0 10px;
          background-color: #f7f8fa;
          border: solid 1px #e5e5e5;
          border-radius: 4px;
        }
      }
      .detail-ctx-content {
        width: 570px;
        border: solid 1px #e5e5e5;
        background-color: #f7f8fa;
        border-radius: 4px;
        padding: 10px;
        .detail-ctx-item {
          margin: 15px 0;
          padding-bottom: 10px;
          .name {
            font-weight: 600;
            color: #333;
          }
          .name + div {
            line-height: 30px;
          }
          border-bottom: solid 1px #e5e5e5;
        }
      }
    }
  }
}
.detail-quote-wrapper {
  border: solid 1px #e5e5e5;
  padding: 10px;
  margin-top: 10px;
  .file-use {
    .rankbox {
      height: 24px;
      display: flex;
      width: fit-content;
      align-items: center;
      justify-content: center;
      color: #2b5fd9;
      background-color: #f0f4ff;
      border: solid 1px #c5d7ff;
      padding: 0 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .file-info {
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
}
.copy-value-dialog {
  .el-dialog__body {
    padding-top: 0;
  }
  .title-input {
    padding: 0 20px 20px 20px;
  }
  .notice {
    background-color: #f0f4ff;
    color: $--color-primary;
    border-radius: 4px;
    margin: 15px 0;
    padding-left: 10px;
  }
  .value-content {
    padding: 10px 0 10px 10px;
    border: solid 1px #e5e5e5;
    border-radius: 4px;
    user-select: all;
  }
}
</style>

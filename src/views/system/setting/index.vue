<template>
  <div class="system-set-container">
    <div class="v-m-t-60 system-set-form">
      <el-form
        ref="systemSetForm"
        :model="systemForm"
        :rules="rules"
        :label-width="labelWidth"
      >
        <div class="pic-upload-wrap">
          <el-form-item :label="t('setting.systemLogo')" prop="logoUrl">
            <imageUpload
              :isAbsoluteUrl="true"
              :limit="1"
              v-model="systemForm.logoUrl"
            />
          </el-form-item>
        </div>
        <el-form-item :label="t('setting.systemNameZh')" prop="bigTitle">
          <el-input
            v-model="systemForm.bigTitle"
            :placeholder="t('setting.systemNameZhPlaceholder')"
            clearable
            style="width: 60%"
          />
        </el-form-item>

        <el-form-item :label="t('setting.systemNameEn')" prop="smallTitle">
          <el-input
            v-model="systemForm.smallTitle"
            :placeholder="t('setting.systemNameEnPlaceholder')"
            clearable
            style="width: 60%"
          />
        </el-form-item>

        <el-form-item style="width: 80%; margin: 40px auto 20px">
          <div>
            <el-button
              :loading="loading"
              size="large"
              class="submit-btn"
              type="primary"
              @click.prevent="handleSave"
            >
              <span>{{ t('common.save') }}</span>
            </el-button>
            <el-button
              size="large"
              class="cancel-btn"
              type="primary"
              @click.prevent="handleCancel"
            >
              <span>{{ t('common.cancel') }}</span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from "vue";
import { t } from '@/utils/i18n';
import { updateSystemInfo } from "@/api/system/setting";
import useSettingsStore from "@/store/modules/settings";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from '@/composables/useLanguageChange'

const settingsStore = useSettingsStore();
const languageStore = useLanguageStore();
const { proxy } = getCurrentInstance();
const loading = ref(false);

const labelWidth = computed(() => {
  return languageStore.currentLanguage === 'zh' ? '120px' : '180px';
});

const data = reactive({
  systemForm: {
    logoUrl: "",
    bigTitle: "",
    smallTitle: "",
  },
  rules: {
    bigTitle: [
      { required: true, message: t('setting.systemNameZhRequired'), trigger: "blur" },
    ],
  },
});

const { systemForm, rules } = toRefs(data);

const fetchSystemInfo = async () => {
  try {
    const response = await settingsStore.getSystemData();
    Object.assign(systemForm.value, response.data);

    systemForm.value.logoUrl = `${window.location.protocol}//${window.location.host}/file/${response.data.logoUrl}`;
  } catch (error) {
    proxy.$modal.msgError(t('setting.getSystemInfoFailed'));
  }
};

const handleSave = async () => {
  try {
    loading.value = true;
    let params = {
      ...systemForm.value,
      logoUrl: systemForm.value.logoUrl.split(
        `${window.location.protocol}//${window.location.host}/file/`
      )[1],
    };
    await updateSystemInfo(params);
    proxy.$modal.msgSuccess(t('setting.saveSystemInfoSuccess'));
    settingsStore.getSystemData();
  } catch (error) {
    proxy.$modal.msgError(t('setting.saveSystemInfoFailed'));
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  fetchSystemInfo();
};

onMounted(() => {
  fetchSystemInfo();
});

// Add language change handling
useLanguageChange()
</script>

<style scoped lang="scss">
.system-set-container {
  width: 100%;
  min-height: calc(100vh - 170px);
  overflow: auto;
  background: linear-gradient(
      1turn,
      rgba(140, 201, 56, 0.2),
      rgba(140, 201, 56, 0) 20%
    ),
    linear-gradient(180deg, #8dcfeb, #fefeff);

  .system-set-form {
    width: 60%;
    margin: 0 auto;
  }

  .pic-upload-wrap {
    background: hsla(0, 0%, 100%, 0.35);
    padding: 40px 20px 10px 20px;
    margin-bottom: 20px;
  }
}
</style>

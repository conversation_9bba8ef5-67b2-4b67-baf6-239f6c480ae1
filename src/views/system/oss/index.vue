<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" :label-width="labelWidth">
      <el-form-item :label="t('oss.fileName')" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          :placeholder="t('oss.fileNamePlaceholder')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('oss.originalName')" prop="originalName">
        <el-input
          v-model="queryParams.originalName"
          :placeholder="t('oss.originalNamePlaceholder')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('oss.fileSuffix')" prop="fileSuffix">
        <el-input
          v-model="queryParams.fileSuffix"
          :placeholder="t('oss.fileSuffixPlaceholder')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('oss.createTime')">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('oss.startDate')"
          :end-placeholder="t('oss.endDate')"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="t('oss.createBy')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="t('oss.createByPlaceholder')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('oss.service')" prop="service">
        <el-input
          v-model="queryParams.service"
          :placeholder="t('oss.servicePlaceholder')"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="search" @click="handleQuery">{{ t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleFile"
          v-hasPermi="['system:oss:upload']"
        >{{ t('oss.uploadFile') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleImage"
          v-hasPermi="['system:oss:upload']"
        >{{ t('oss.uploadImage') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:oss:remove']"
        >{{ t('common.delete') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          :type="previewListResource ? 'danger' : 'warning'"
          plain
          @click="handlePreviewListResource(!previewListResource)"
          v-hasPermi="['system:oss:edit']"
        >{{ t('oss.previewSwitch') }} : {{previewListResource ? t('oss.disable') : t('oss.enable')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Operation"
          @click="handleOssConfig"
          v-hasPermi="['system:oss:list']"
        >{{ t('oss.configManagement') }}</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ossList" @selection-change="handleSelectionChange"
              :header-cell-class-name="handleHeaderClass"
              @header-click="handleHeaderCLick"
              v-if="showTable">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="t('oss.ossId')" align="center" prop="ossId" v-if="false"/>
      <el-table-column :label="t('oss.fileName')" align="center" prop="fileName" />
      <el-table-column :label="t('oss.originalName')" align="center" prop="originalName" />
      <el-table-column :label="t('oss.fileSuffix')" align="center" prop="fileSuffix" />
      <el-table-column :label="t('oss.filePreview')" align="center" prop="url">
        <template #default="scope">
          <ImagePreview
            v-if="previewListResource && checkFileSuffix(scope.row.fileSuffix)"
            :width="100" :height="100"
            :src="scope.row.url"
            :preview-src-list="[scope.row.url]"/>
          <span v-text="scope.row.url"
                v-if="!checkFileSuffix(scope.row.fileSuffix) || !previewListResource"/>
        </template>
      </el-table-column>
      <el-table-column :label="t('oss.createTime')" align="center" prop="createTime" width="180" sortable="custom">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('oss.createBy')" align="center" prop="createBy" />
      <el-table-column :label="t('oss.service')" align="center" prop="service" sortable="custom"/>
      <el-table-column :label="t('common.operation')" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleDownload(scope.row)" v-hasPermi="['system:oss:download']">{{ t('oss.download') }}</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:oss:remove']">{{ t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改OSS对象存储对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="ossRef" :model="form" :rules="rules" :label-width="labelWidth">
        <el-form-item :label="t('oss.fileName')">
          <fileUpload v-model="form.file" v-if="type === 0"/>
          <imageUpload v-model="form.file" v-if="type === 1"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">{{ t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Oss">
import { ref, watch, computed, onMounted, getCurrentInstance } from "vue";
import { ElMessageBox } from "element-plus";
import { t } from '@/utils/i18n';
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import useLanguageStore from "@/store/modules/language";
import { useRoute, useRouter } from 'vue-router';
import { listOss, delOss } from "@/api/system/oss";
import ImagePreview from "@/components/ImagePreview/index.vue";
import { useLanguageChange } from '@/composables/useLanguageChange'

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const languageStore = useLanguageStore();

const labelWidth = computed(() => {
  return languageStore.currentLanguage === 'zh' ? '68px' : '120px';
});

const ossList = ref([]);
const open = ref(false);
const showTable = ref(true);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const type = ref(0);
const previewListResource = ref(true);
const daterangeCreateTime = ref([]);
// 默认排序
const defaultSort = ref({prop: 'createTime', order: 'ascending'});

const data = reactive({
  form: {},
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    fileName: undefined,
    originalName: undefined,
    fileSuffix: undefined,
    url: undefined,
    createTime: undefined,
    createBy: undefined,
    service: undefined
  },
  rules: {
    file: [
      { required: true, message: t('oss.fileRequired'), trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// Add language change handling
useLanguageChange()

/** 查询OSS对象存储列表 */
function getList() {
  loading.value = true;
  proxy.getConfigKey("sys.oss.previewListResource").then(response => {
    previewListResource.value = response.msg === undefined ? true : response.msg === 'true';
  });
  listOss(proxy.addDateRange(queryParams.value, daterangeCreateTime.value, "CreateTime")).then(response => {
    ossList.value = response.rows;
    total.value = response.total;
    loading.value = false;
    showTable.value = true;
  });
}
function checkFileSuffix(fileSuffix) {
  let arr = ["png", "jpg", "jpeg"];
  return arr.some(type => {
    return fileSuffix.indexOf(type) > -1;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    file: undefined,
  };
  proxy.resetForm("ossRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  showTable.value = false;
  daterangeCreateTime.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.orderByColumn = defaultSort.value.prop;
  queryParams.value.isAsc = defaultSort.value.order;
  handleQuery();
}
/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.ossId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
// 设置列的排序为我们自定义的排序
function handleHeaderClass({column}) {
  column.order = column.multiOrder
}
// 点击表头进行排序
function handleHeaderCLick(column) {
  if (column.sortable !== 'custom') {
    return
  }
  switch (column.multiOrder) {
    case 'descending':
      column.multiOrder = 'ascending';
      break;
    case 'ascending':
      column.multiOrder = '';
      break;
    default:
      column.multiOrder = 'descending';
      break;
  }
  handleOrderChange(column.property, column.multiOrder)
}
function handleOrderChange(prop, order) {
  let orderByArr = queryParams.value.orderByColumn ? queryParams.value.orderByColumn.split(",") : [];
  let isAscArr = queryParams.value.isAsc ? queryParams.value.isAsc.split(",") : [];
  let propIndex = orderByArr.indexOf(prop)
  if (propIndex !== -1) {
    if (order) {
      //排序里已存在 只修改排序
      isAscArr[propIndex] = order;
    } else {
      //如果order为null 则删除排序字段和属性
      isAscArr.splice(propIndex, 1);//删除排序
      orderByArr.splice(propIndex, 1);//删除属性
    }
  } else {
    //排序里不存在则新增排序
    orderByArr.push(prop);
    isAscArr.push(order);
  }
  //合并排序
  queryParams.value.orderByColumn = orderByArr.join(",");
  queryParams.value.isAsc = isAscArr.join(",");
  getList();
}
/** 任务日志列表查询 */
function handleOssConfig() {
  router.push('/system/oss-config/index')
}
/** 文件按钮操作 */
function handleFile() {
  reset();
  open.value = true;
  title.value = "上传文件";
  type.value = 0;
}
/** 图片按钮操作 */
function handleImage() {
  reset();
  open.value = true;
  title.value = "上传图片";
  type.value = 1;
}
/** 提交按钮 */
function submitForm() {
  open.value = false;
  getList();
}
/** 下载按钮操作 */
function handleDownload(row) {
  proxy.$download.oss(row.ossId)
}
/** 用户状态修改  */
function handlePreviewListResource(previewListResource) {
  let text = previewListResource ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""预览列表图片"配置吗?').then(() => {
    return proxy.updateConfigByKey("sys.oss.previewListResource", previewListResource);
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {});
}
/** 删除按钮操作 */
function handleDelete(row) {
  const ossIds = row.ossId || ids.value;
  proxy.$modal.confirm('是否确认删除OSS对象存储编号为"' + ossIds + '"的数据项?').then(() => {
    loading.value = true;
    return delOss(ossIds);
  }).then(() => {
    loading.value = false;
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).finally(() => {
    loading.value = false;
  });
}

getList();
</script>

<template>
  <el-form ref="pwdRef" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="t('profile.oldPassword')" prop="oldPassword">
      <el-input
        v-model="user.oldPassword"
        :placeholder="t('profile.oldPasswordPlaceholder')"
        type="password"
        show-password
      />
    </el-form-item>
    <el-form-item :label="t('profile.newPassword')" prop="newPassword">
      <el-input
        v-model="user.newPassword"
        :placeholder="t('profile.newPasswordPlaceholder')"
        type="password"
        show-password
      />
    </el-form-item>
    <el-form-item :label="t('profile.confirmPassword')" prop="confirmPassword">
      <el-input
        v-model="user.confirmPassword"
        :placeholder="t('profile.confirmPasswordPlaceholder')"
        type="password"
        show-password
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">{{
        t("common.save")
      }}</el-button>
      <el-button type="danger" @click="close">{{
        t("common.close")
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { updateUserPwd } from "@/api/system/user";
import { t } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from "@/composables/useLanguageChange";

const { proxy } = getCurrentInstance();

// 语言相关
const languageStore = useLanguageStore();
const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

const user = reactive({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined,
});

const equalToPassword = (rule, value, callback) => {
  if (user.newPassword !== value) {
    callback(new Error(t("profile.passwordMismatch")));
  } else {
    callback();
  }
};
const rules = computed(() => ({
  oldPassword: [
    {
      required: true,
      message: t("profile.oldPasswordRequired"),
      trigger: "blur",
    },
  ],
  newPassword: [
    {
      required: true,
      message: t("profile.newPasswordRequired"),
      trigger: "blur",
    },
    { min: 6, max: 20, message: t("profile.passwordLength"), trigger: "blur" },
  ],
  confirmPassword: [
    {
      required: true,
      message: t("profile.confirmPasswordRequired"),
      trigger: "blur",
    },
    { required: true, validator: equalToPassword, trigger: "blur" },
  ],
}));

/** 提交按钮 */
function submit() {
  proxy.$refs.pwdRef.validate((valid) => {
    if (valid) {
      updateUserPwd(user.oldPassword, user.newPassword).then((response) => {
        proxy.$modal.msgSuccess(t("profile.updateSuccess"));
      });
    }
  });
}
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
}
</script>

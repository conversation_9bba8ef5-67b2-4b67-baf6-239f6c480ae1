<template>
  <el-form ref="userRef" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="t('profile.nickname')" prop="nickName">
      <el-input v-model="user.nickName" maxlength="30" />
    </el-form-item>
    <el-form-item :label="t('profile.phoneNumber')" prop="phonenumber">
      <el-input v-model="user.phonenumber" maxlength="11" />
    </el-form-item>
    <el-form-item :label="t('profile.email')" prop="email">
      <el-input v-model="user.email" maxlength="50" />
    </el-form-item>
    <el-form-item :label="t('profile.gender')">
      <el-radio-group v-model="user.sex">
        <el-radio label="0">{{ t("profile.male") }}</el-radio>
        <el-radio label="1">{{ t("profile.female") }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">{{
        t("common.save")
      }}</el-button>
      <el-button type="danger" @click="close">{{
        t("common.close")
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { updateUserProfile } from "@/api/system/user";
import { t } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";
import { useLanguageChange } from "@/composables/useLanguageChange";

const props = defineProps({
  user: {
    type: Object,
  },
});

const { proxy } = getCurrentInstance();

// 语言相关
const languageStore = useLanguageStore();
const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

const rules = computed(() => ({
  nickName: [
    { required: true, message: t("profile.nicknameRequired"), trigger: "blur" },
  ],
  email: [
    { required: true, message: t("profile.emailRequired"), trigger: "blur" },
    {
      type: "email",
      message: t("profile.emailFormat"),
      trigger: ["blur", "change"],
    },
  ],
  phonenumber: [
    { required: true, message: t("profile.phoneRequired"), trigger: "blur" },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: t("profile.phoneFormat"),
      trigger: "blur",
    },
  ],
}));

/** 提交按钮 */
function submit() {
  proxy.$refs.userRef.validate((valid) => {
    if (valid) {
      updateUserProfile(props.user).then((response) => {
        proxy.$modal.msgSuccess(t("profile.updateSuccess"));
      });
    }
  });
}
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
}
</script>

<template>
  <div>
    <div ref="graphContainer" class="graph-container"></div>
    <div class="button-container v-flex v-row-center">
      <el-button
        :disabled="index == 0"
        icon="ArrowLeftBold"
        type="success"
        @click="preImage"
        >上一张</el-button
      >
      <el-button icon="Folder" type="success" @click="saveImage"
        >保存图片</el-button
      >
      <span class="v-p-r-10" v-if="methods.length > 0"
        >method: {{ methods[index] ?? "" }}</span
      >
      <el-button icon="DocumentDelete" type="success" @click="clearCanvas"
        >清除画布</el-button
      >
      <el-button
        :disabled="index + 1 == methods.length"
        type="success"
        @click="nextImage"
        >下一张<el-icon><ArrowRightBold /></el-icon
      ></el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  title: {
    type: String,
    default: "风险节点中心性分析",
  },
  data: {
    type: Object,
    default: () => ({
      nodes: [],
      links: [],
    }),
  },
  methods: {
    type: Array,
    default: () => [],
  },
  index: {
    type: Number,
    default: 0,
  },
  visualMapColors: {
    type: Array,
    default: () => ["#d94e5d", "#eac736", "#50a3ba"],
  },
  nodeColors: {
    type: Object,
    default: () => ({
      1: "#d94e5d",
      2: "#eac736",
      3: "#50a3ba",
      4: "#8bc34a",
      5: "#03A9F4",
      6: "#9c27b0",
    }),
  },
  chartType: {
    type: String,
    default: "visualMap", // 可选值: "visualMap" 或 "legend"
  },
});

const emit = defineEmits(["updateData"]);

const graphContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(graphContainer.value);

  const nodeCount = props.data.nodes.length;
  const maxSymbolSize = 50; // 最大节点大小
  const minSymbolSize = 10; // 最小节点大小
  const maxValue = Math.max(
    ...props.data.nodes.map((node) => parseFloat(node.value))
  );
  const minValue = Math.min(
    ...props.data.nodes.map((node) => parseFloat(node.value))
  );

  const getSymbolSize = (value) => {
    return maxValue - minValue === 0
      ? 24
      : minSymbolSize +
          ((maxSymbolSize - minSymbolSize) * (value - minValue)) /
            (maxValue - minValue);
  };

  const getColor = (value) => {
    const colorScale = echarts.color.lerp(
      (value - minValue) / (maxValue - minValue),
      props.visualMapColors
    );
    return colorScale;
  };

  const option = {
    title: {
      text: `${props.title}`,
      left: "center",
      textStyle: {
        color: "#333", // 设置标题颜色
        fontSize: "16px",
        fontWeight: "normal",
      },
    },
    tooltip: {},
    legend:
      props.chartType === "legend"
        ? {
            data: Object.keys(props.nodeColors),
            right: 10,
            top: 10,
            orient: "vertical",
            textStyle: {
              color: (name) => props.nodeColors[name], // 设置图例颜色
            },
          }
        : undefined,
    visualMap:
      props.chartType === "visualMap"
        ? {
            show: true,
            min: minValue,
            max: maxValue,
            dimension: 0,
            inRange: {
              color: props.visualMapColors,
            },
            right: 10,
            bottom: 10,
          }
        : undefined,
    series: [
      {
        // 图表类型为关系图
        type: "graph",
        // 使用力导向布局
        layout: "force",
        // 处理节点数据
        data: props.data.nodes.map((node) => ({
          ...node,
          symbolSize: getSymbolSize(parseFloat(node.value)), // 动态调整节点大小
          // 设置节点样式
          itemStyle: {
            color:
              props.chartType === "visualMap"
                ? getColor(parseFloat(node.value)) // 使用视觉映射颜色
                : props.nodeColors[node.type], // 使用预定义的节点颜色
            emphasis: {
              borderColor: "#333", // 高亮时节点的边框颜色
              borderWidth: 2, // 高亮时节点的边框宽度
            },
          },
          category: node.type, // 设置节点的类别
        })),
        // 设置节点之间的连接关系
        links: props.data.links,
        // 定义节点类别
        categories: Object.keys(props.nodeColors).map((type) => ({
          name: type,
          itemStyle: {
            color: props.nodeColors[type], // 设置类别颜色
          },
        })),
        // 连接线的样式配置
        lineStyle: {
          // color: "source",
          opacity: 0.4,
          curveness: 0.2, // 曲率值 0-1，可自行调整
          type: "curve", // 明确指定曲线类型
          width: 2, // 线的粗细
          emphasis: {
            color: "source",
            width: 3, // 高亮时线的粗细
            opacity: 1, // 高亮时线的透明度
          },
        },
        // 允许图表缩放和平移
        roam: true,
        // 节点标签配置
        label: {
          show: true,
        },
        // 力导向布局相关的配置
        force: {
          repulsion: Math.max(100, 500 / nodeCount), // 动态调整节点之间的排斥力
          edgeLength: [50, 200], // 动态调整边的长度范围
          gravity: 0.15, // 调整重力以确保节点不会溢出容器
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

const saveImage = () => {
  if (chartInstance) {
    const img = chartInstance.getDataURL({
      type: "png",
      pixelRatio: 2,
      backgroundColor: "#fff",
    });
    const link = document.createElement("a");
    link.href = img;
    link.download = "graph.png";
    link.click();
  }
};

const clearCanvas = () => {
  if (chartInstance) {
    chartInstance.clear();
  }
};

const preImage = () => {
  emit("updateData", "pre");
};

const nextImage = () => {
  emit("updateData", "next");
};

onMounted(() => {
  initChart();
});

watch(
  () => props.data,
  () => {
    initChart();
  },
  { deep: true, debounce: 100 }
);

watch(
  () => props.chartType,
  () => {
    initChart();
  }
);

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
.graph-container {
  width: 100%;
  height: 430px;
  overflow: hidden; /* 确保容器内的内容不会溢出 */
}

.button-container {
  margin-top: 10px;
}

button {
  margin-right: 10px;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
}
</style>

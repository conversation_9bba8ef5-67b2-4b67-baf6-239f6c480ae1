<template>
  <div>
    <div ref="graphContainer" class="graph-container"></div>
    <div class="button-container v-flex v-row-center">
      <el-button icon="ArrowLeftBold" type="success" @click="preImage"
        >上一张</el-button
      >
      <el-button icon="Folder" type="success" @click="saveImage"
        >保存图片</el-button
      >
      <el-button icon="DocumentDelete" type="success" @click="clearCanvas"
        >清除画布</el-button
      >
      <el-button type="success" @click="nextImage"
        >下一张<el-icon><ArrowRightBold /></el-icon
      ></el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, watch } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  title: {
    type: String,
    default: "KMeans Clustering",
  },
  data: {
    type: Object,
    default: () => ({
      nodes: [
        { name: "1", value: 10, type: "A" },
        { name: "2", value: 20, type: "B" },
        { name: "3", value: 30, type: "A" },
        { name: "4", value: 40, type: "B" },
        { name: "5", value: 50, type: "A" },
        { name: "6", value: 60, type: "B" },
        { name: "7", value: 70, type: "A" },
        { name: "8", value: 80, type: "B" },
        { name: "9", value: 90, type: "A" },
        { name: "10", value: 100, type: "B" },
        { name: "11", value: 110, type: "A" },
        { name: "12", value: 120, type: "B" },
        { name: "13", value: 130, type: "A" },
        { name: "14", value: 140, type: "B" },
        { name: "15", value: 150, type: "A" },
        { name: "16", value: 160, type: "B" },
        { name: "17", value: 170, type: "A" },
        { name: "18", value: 180, type: "B" },
        { name: "19", value: 190, type: "A" },
        { name: "20", value: 200, type: "B" },
      ],
      links: [
        { source: "1", target: "2" },
        { source: "2", target: "3" },
        { source: "3", target: "4" },
        { source: "4", target: "5" },
        { source: "5", target: "6" },
        { source: "6", target: "7" },
        { source: "7", target: "8" },
        { source: "8", target: "9" },
        { source: "9", target: "10" },
        { source: "10", target: "11" },
        { source: "11", target: "12" },
        { source: "12", target: "13" },
        { source: "13", target: "14" },
        { source: "14", target: "15" },
        { source: "15", target: "16" },
        { source: "16", target: "17" },
        { source: "17", target: "18" },
        { source: "18", target: "19" },
        { source: "19", target: "20" },
        { source: "20", target: "1" },
      ],
    }),
  },
  nodeColors: {
    type: Object,
    default: () => ({
      A: "#ff0000",
      B: "#0000ff",
    }),
  },
});

const emit = defineEmits(["updateData"]);

const graphContainer = ref(null);
let chartInstance = null;

const initChart = () => {
  chartInstance = echarts.init(graphContainer.value);

  const nodeCount = props.data.nodes.length;
  const symbolSize = Math.max(20, 100 / nodeCount); // 动态计算节点大小
  const repulsion = Math.max(100, 500 / nodeCount); // 动态计算节点之间的排斥力

  const option = {
    title: {
      text: props.title,
      left: "center",
      textStyle: {
        color: "#333", // 设置标题颜色
        fontSize: "16px",
        fontWeight: "normal",
      },
    },
    tooltip: {},
    legend: {
      data: Object.keys(props.nodeColors),
      right: 10,
      top: 10,
      orient: "vertical",
    },
    series: [
      {
        type: "graph",
        layout: "force",
        data: props.data.nodes.map((node) => ({
          ...node,
          symbolSize: symbolSize, // 动态调整节点大小
          itemStyle: {
            color: props.nodeColors[node.type],
          },
          category: node.type, // 设置节点的类别
        })),
        links: props.data.links,
        categories: Object.keys(props.nodeColors).map((type) => ({
          name: type,
        })),
        roam: true,
        label: {
          show: true,
        },
        force: {
          repulsion: repulsion, // 动态调整节点之间的排斥力
          edgeLength: [50, 200], // 动态调整边的长度范围
          gravity: 0.1, // 调整重力以确保节点不会溢出容器
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

const saveImage = () => {
  if (chartInstance) {
    const img = chartInstance.getDataURL({
      type: "png",
      pixelRatio: 2,
      backgroundColor: "#fff",
    });
    const link = document.createElement("a");
    link.href = img;
    link.download = "graph.png";
    link.click();
  }
};

const clearCanvas = () => {
  if (chartInstance) {
    chartInstance.clear();
  }
};

const preImage = () => {
  emit("updateData", "pre");
};

const nextImage = () => {
  emit("updateData", "next");
};

onMounted(() => {
  initChart();
});

watch(
  () => props.data,
  () => {
    initChart();
  },
  { deep: true }
);
</script>

<style scoped>
.graph-container {
  width: 100%;
  height: 340px;
  overflow: hidden; /* 确保容器内的内容不会溢出 */
}

.button-container {
  margin-top: 10px;
}

button {
  margin-right: 10px;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
}
</style>

<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-tooltip class="item" effect="dark" :content="showSearch ? t('toolbar.hideSearch') : t('toolbar.showSearch')" placement="top" v-if="search">
        <el-button circle icon="Search" @click="toggleSearch()" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="t('toolbar.refresh')" placement="top">
        <el-button circle icon="Refresh" @click="refresh()" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="t('toolbar.showHideColumns')" placement="top" v-if="columns">
        <el-button circle icon="Menu" @click="showColumn()" />
      </el-tooltip>
    </el-row>
    <el-dialog :title="t('toolbar.showHideTitle')" v-model="open" append-to-body>
      <el-transfer
        :titles="[t('toolbar.show'), t('toolbar.hide')]"
        v-model="value"
        :data="columns"
        :props="{ key: 'prop', label: 'label' }"
      ></el-transfer>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCheckValue">{{ t('common.confirm') }}</el-button>
          <el-button @click="cancelCheck">{{ t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { t } from '@/utils/i18n';

const props = defineProps({
  showSearch: {
    type: Boolean,
    default: true
  },
  columns: {
    type: Array
  },
  search: {
    type: Boolean,
    default: true
  },
  gutter: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['update:showSearch', 'queryTable']);

const style = computed(() => {
  const ret = {};
  if (props.gutter) {
    ret.marginRight = `${props.gutter}px`;
  }
  return ret;
});

// 显隐数据
const value = ref([]);
// 是否显示弹出层
const open = ref(false);

// 搜索
function toggleSearch() {
  emit('update:showSearch', !props.showSearch);
}

// 刷新
function refresh() {
  emit('queryTable');
}

// 右侧列表元素变化
function submitCheckValue() {
  emit('queryTable');
  open.value = false;
}

// 取消按钮
function cancelCheck() {
  open.value = false;
  emit('queryTable');
}

// 打开显隐列dialog
function showColumn() {
  open.value = true;
}
</script>

<style lang="scss" scoped>
.right-menu {
  float: right;
  height: 100%;
  line-height: 50px;
  margin: 0;
  &:focus {
    outline: none;
  }
  .right-menu-item {
    display: inline-block;
    padding: 0 8px;
    height: 100%;
    font-size: 18px;
    color: #5a5e66;
    vertical-align: text-bottom;
    &.hover-effect {
      cursor: pointer;
      transition: background 0.3s;
      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }
  }
}
</style>
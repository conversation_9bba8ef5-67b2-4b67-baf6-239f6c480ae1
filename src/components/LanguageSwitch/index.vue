<template>
  <el-dropdown @command="handleCommand" trigger="click">
    <span class="language-switch">
      <el-icon><Globe /></el-icon>
      <span class="language-text">{{ currentLanguageText }}</span>
      <el-icon class="el-icon--right"><arrow-down /></el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          command="zh"
          :class="{ active: currentLanguage === 'zh' }"
        >
          <span class="flag-icon">🇨🇳</span>
          {{ t("map.chinese") }}
        </el-dropdown-item>
        <el-dropdown-item
          command="en"
          :class="{ active: currentLanguage === 'en' }"
        >
          <span class="flag-icon">🇺🇸</span>
          {{ t("map.english") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed } from "vue";
// import { Globe, ArrowDown } from '@element-plus/icons-vue'
import { t, setLanguage, getLanguage } from "@/utils/i18n";
import useLanguageStore from "@/store/modules/language";

const languageStore = useLanguageStore();

const currentLanguage = computed(() => languageStore.currentLanguage);

const currentLanguageText = computed(() => {
  return currentLanguage.value === "zh" ? t("map.chinese") : t("map.english");
});

const handleCommand = (command) => {
  if (command !== currentLanguage.value) {
    setLanguage(command);
    // 刷新页面以确保所有组件都更新
    window.location.reload();
  }
};
</script>

<style scoped>
.language-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.language-switch:hover {
  background-color: var(--el-color-primary-light-9);
}

.language-text {
  margin: 0 4px;
  font-size: 14px;
}

.flag-icon {
  margin-right: 8px;
  font-size: 16px;
}

.el-dropdown-menu__item.active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>

<template>
  <div class="ai commoonBtn icon-btn" @click="showAIDialog">
    <img class="origin-dom" src="@/assets/images/ai.gif " alt="" />
    <div class="h-name arial-text">
      <div class="clrcle"></div>
      AI
    </div>
    <Transition name="fade">
      <ai-popover @closeAi="closeAi" v-if="showAiPopover"></ai-popover>
    </Transition>
  </div>
</template>

<script setup>
import aiPopover from "./aiPopover.vue";
const showAiPopover = ref(false);
function showAIDialog() {
  showAiPopover.value = true;
}
</script>

<style scoped lang="scss">
.ai {
  bottom: 73px;
  background-color: #fff;
  position: absolute;
  right: 0;
  bottom: 40px;

  > img {
    width: 28px;
    height: 24px;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
}

.icon-btn {
  // position: relative;
  width: 40px;
  height: 40px;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(161, 167, 179, 0.3);
  border-radius: 8px 0 0 8px;
  // padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .h-name {
    font-weight: 500;
    color: #039855;
    font-size: 12px;
    line-height: 14px;
    font-family: PingFangSC, PingFang SC;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
  }

  .origin-dom {
    display: block;
  }

  .arial-text {
    font-size: 24px;
    font-family: Arial, Arial;
    font-weight: normal;
    color: #039855;
    font-style: italic;
    font-weight: 600;

    .clrcle {
      position: absolute;
      width: 28px;
      height: 28px;
      background: #0f998214;
      border-radius: 50%;
    }
  }

  i {
    font-size: 24px;
  }

  &:hover {
    .origin-dom {
      display: none;
    }

    .h-name {
      display: flex;
    }
  }

  .active-sapn {
    color: #039855;
  }
}
</style>

<template>
  <div class="anwser-wrapper">
    <div class="anwser-box">
      <div class="loading" v-if="anwser.value[0].text.content === ''">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div v-else>
        <span>
          <span v-if="anwser.value[0].text.content">回答：</span>
          <span v-html="getValue"></span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useMarkdown } from "../../views/deepSeek/hooks/markdown";
const props = defineProps({
  anwser: {
    type: Object,
  },
});
const { marked } = useMarkdown();
const getValue = computed(() => {
  return marked(props.anwser.value[0].text.content ?? "");
});
</script>

<style scoped lang="scss">
.anwser-wrapper {
  display: flex;
  width: 100%;
  margin-bottom: 12px;

  .anwser-box {
    border-radius: 20px 25px 25px 4px;
    background: linear-gradient(213deg, #f3f5f8 24.37%, #fff 80.13%), #fff;
    box-shadow: 0 8px 20px #d2dae340, -6px 7px 24px #d2dae31a, 10px 5px 15px #d2dae33d, inset 0 12px 12px #edf1f542, inset 1px -1px #fff, inset 0 1px #fffffff0;
    display: -ms-flexbox;
    display: flex;
    max-width: 300px;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 12px 20px;
    gap: 12px;
    font-family: PingFangSC, PingFang SC;
    color: #333333;
  }

  .think-content {
    color: #9099a2;
  }

  .loading {
    width: 150px;
    height: 15px;
    margin: 0 auto;
    margin-top: 0;
  }

  .loading span {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 5px;
    border-radius: 50%;
    background: #4d88ff;
    animation: load 1.04s ease infinite;
  }

  .loading span:last-child {
    margin-right: 0px;
  }

  @keyframes load {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  .loading span:nth-child(1) {
    animation-delay: 0.13s;
  }

  .loading span:nth-child(2) {
    animation-delay: 0.26s;
  }

  .loading span:nth-child(3) {
    animation-delay: 0.39s;
  }

  .loading span:nth-child(4) {
    animation-delay: 0.52s;
  }

  .loading span:nth-child(5) {
    animation-delay: 0.65s;
  }
}
</style>

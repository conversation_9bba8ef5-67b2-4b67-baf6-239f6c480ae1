<template>
  <div class="ask-wrapper">
    <div class="ask-box">
      {{ askInfo.value[0].text.content }}
    </div>
  </div>
</template>

<script setup>
defineProps({
  askInfo: {
    type: Object,
  },
});
</script>

<style scoped lang="scss">
.ask-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;

  .ask-box {
    border-radius: 25px 20px 4px 25px;
    display: flex;
    max-width: 300px;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 12px 20px;
    gap: 12px;
    background: linear-gradient(213deg, #f3f5f8 24.37%, #fff 80.13%), #fff;
    box-shadow: 0 8px 20px #d2dae340, -6px 7px 24px #d2dae31a, 10px 5px 15px #d2dae33d, inset 0 12px 12px #edf1f542, inset 1px -1px #fff;
    font-family: PingFangSC, PingFang SC;
    color: #333333;
  }
}
</style>

<template>
  <div class="ai-wrapper" @click.stop.prevent="">
    <div class="title">
      <div class="img-box">
        <img src="@/assets/images/ai.gif" alt="" />
      </div>
      <span>Hi，我是AI问答助手</span>
      <div class="close" @click="close">
        <i class="iconfont icon-guanbi"></i>
      </div>
    </div>
    <div class="ask-input">
      <el-input style="width: 100%; height: 100%" autosize v-model="askContent" @keyup.enter="chatStart" placeholder="请输入关键字">
        <template #prefix>
          <div @click="search" class="search-icon">
            <i class="iconfont icon-sousuo"></i>
          </div>
        </template>
      </el-input>
    </div>
    <div class="chat-content" ref="contentRef">
      <el-scrollbar style="height: 100%" ref="scrollRef">
        <template v-if="!chatList.length">
          <div class="fast-wrapper">
            <div class="title">你可以这样问我：</div>
            <div class="fast-item" v-for="(item, index) in fastAskList" :key="index" @click="fastSearch(item)">
              <img src="@/assets/images/dot.png" alt="" />
              <span>{{ item.content }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div ref="innerRef">
            {{ item }}
            <template v-for="(item, _idx) in chatList" :key="_idx">
              <my-ask v-if="item.obj === CHAT_ROLE_TYPE.HUMAN" :askInfo="item"></my-ask>
              <ai-anwser v-else :anwser="item"></ai-anwser>
            </template>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div class="btns" v-if="!isInit">
      <div class="btn" @click="back">返回</div>
      <div class="btn" :class="{ 'useful-btn': useful }" @click="help">
        <i v-if="useful" class="iconfont icon-dianzan"></i>
        很有帮助
      </div>
    </div>
  </div>
</template>

<script setup>
import MyAsk from "./myAsk.vue";
import AiAnwser from "./aiAnwser.vue";
import { StreamFetcher } from "../../views/deepSeek/lib";
import { deepClone } from "@/utils";
import { CHAT_STATUS, CHAT_ROLE_TYPE, getChatInfo } from "../../views/deepSeek/hooks/chat";

const streamFetcher = new StreamFetcher();
const askContent = ref("");
const chatList = ref([]);
const chatStatus = reactive({
  text: "加载中...",
  code: CHAT_STATUS.READY,
});

const aiResponse = {
  ...getChatInfo(CHAT_ROLE_TYPE.AI),
  historyPreviewLength: 0,
  responseNodeList: [],
  llmModuleAccount: 1,
  totalQuoteList: [],
  totalRunningTime: 0,
  userGoodFeedback: false,
  userBadFeedback: "",
};
const currentAiResponse = ref({
  ...aiResponse,
});

function chatStart() {
  if (chatStatus.code === CHAT_STATUS.RUNNING) {
    ElMessage({ message: "聊天正在进行，请等待结束", type: "error" });
    return;
  }
  const askText = askContent.value.trim();
  askContent.value = "";
  chatStatus.code = CHAT_STATUS.RUNNING;
  currentAiResponse.value = deepClone(aiResponse);
  chatList.value.push(getChatInfo(CHAT_ROLE_TYPE.HUMAN, askText), currentAiResponse.value);
  askContent.value = "";
  nextTick(() => {
    scrollToBottom();
  });
  console.log("chatList.value++++", chatList.value);
  streamFetcher.start(
    {
      url: "/api/v1/chat/completions",
      options: {
        stream: true,
        detail: true,
        responseChatItemId: currentAiResponse.value.dataId,
        messages: [{ role: "user", content: askText }],
      },
    },
    (obj) => {
      const answerType = obj?.event ?? "";
      switch (answerType) {
        case CHAT_ANSWER:
          currentAiResponse.value.value[0].text.content += obj.data.choices[0].delta?.content ?? "";
          break;
        case CHAT_FLOWNODE:
          chatStatus.code = CHAT_STATUS.RUNNING;
          chatStatus.text = obj.data?.name ?? "";
          break;
        case CHAT_FLOWRESPONSE:
          break;
        default:
          break;
      }
      scrollToBottom();
    },
    () => {
      chatStatus.code = CHAT_STATUS.READY;
      console.log("结束");
      scrollToBottom();
    },
    (err) => {
      console.log("错误", err);
    }
  );
}

// const eTalkType ={
//   ASK = "ASK",
//   ANWSER = "ANWSER"
// }

function back() {
  if (currentTalk.value && !currentTalk.value?.isDone) {
    $message.warning("上述回答还未完成");
    return;
  }
}

const contentRef = ref(null);
const scrollRef = ref(null);

function scrollToBottom() {
  nextTick(() => {});
}
const fastAskList = [
  { content: "物模型库是什么" },
  { content: "多企业如何切换" },
  { content: "平台可以积累哪些数字资产" },
  { content: "讲一下场景联动" },
  // { content: '请介绍一下渲染引擎？' }
];
function close() {
  emits("closeAi");
}

const emits = defineEmits(["closeAi"]);
</script>

<style scoped lang="scss">
.ai-wrapper {
  width: 400px;
  height: 600px;
  background: radial-gradient(39.17% 67.56% at -36.76% 2.47%, #fafffe4d, #f9fffe00), radial-gradient(80.57% 51.85% at 123.03% -5.26%, #4c54ff0d, #4c54ff00), radial-gradient(75.35% 50.82% at 111.67% 5.09%, #1f6bff38, #f3f6fb00), radial-gradient(144.32% 37.42% at 14.23% -5.37%, #65fff640, #f2f5fa00), linear-gradient(180deg, #2f75ff1a, #a7f4ff0a 33.49%, #f6f7f900 99.18%), #f6f7f9;
  border: 1px solid #fff;
  box-shadow: 8px 19px 38px #20252f12;
  position: absolute;
  right: calc(100% + 10px);
  bottom: 0px;
  border-radius: 12px;
  padding: 20px 30px 30px 30px;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  // background-color: unset;

  .title {
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;

    .img-box {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url("../../assets/images/box.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      -webkit-box-reflect: below 2px -webkit-linear-gradient(transparent, transparent 50%, rgba(0, 0, 0, 0.6));

      > img {
        width: 28px;
        height: 24px;
      }
    }

    > span {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #333333;
      margin-left: 17px;
    }

    .close {
      cursor: pointer;
      position: absolute;
      right: -10px;
      top: -10px;
    }
  }

  .ask-input {
    width: 100%;
    height: 40px;
    margin: 24px 0 30px 0;
    flex: none;

    .n-input {
      border-radius: 20px;
    }
  }

  .chat-content {
    width: 100%;
    height: calc(100% - 200px);
    flex: 1;
    // border: solid 1px red;

    .fast-wrapper {
      .title {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        color: #8d9199;
      }

      .fast-item {
        width: 230px;
        height: 46px;
        background: linear-gradient(180deg, #f2f4f7 0%, #ffffff 100%);
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
        border-radius: 20px 100px 100px 4px;
        border: 1px solid #ffffff;
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        cursor: pointer;

        > img {
          width: 20px;
          height: 20px;
          margin: 0 6px 0 14px;
        }
      }
    }
  }

  .btns {
    display: flex;
    align-items: center;
    margin: 30px 0 0 0;
    justify-content: space-between;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 160px;
      height: 40px;
      border-radius: 20px;
      border: 1px solid #d9dadb;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      color: #333333;
      cursor: pointer;

      &:hover {
        border: solid 1px #039855;
        color: #039855;
      }
    }

    .useful-btn {
      border: solid 1px #039855;
      color: #039855;
    }
  }
}
</style>

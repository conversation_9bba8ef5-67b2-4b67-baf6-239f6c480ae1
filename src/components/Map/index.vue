<template>
  <div
    v-if="!dialogVisible"
    id="icingliveMap"
    class="icingliveMap"
    :style="{ width: width, height: height }"
  ></div>

  <div class="fullscreen" @click="openScreen">
    <img src="@/assets/images/fullscreen.png" />
  </div>

  <el-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    fullscreen
    @close="closeScreen"
  >
    <div
      id="icingliveMap2"
      class="icingliveMap"
      style="width: 100vw; height: 80vh"
    ></div>
  </el-dialog>
</template>

<script setup>
import "ol/ol.css";
import * as echarts from "echarts";
import { Map, View, Tile, Feature, Overlay } from "ol";
import {
  XYZ,
  Vector as VectorSource,
  ImageStatic,
  ImageCanvas as ImageCanvasSource,
  Cluster,
} from "ol/source";
import {
  Tile as TileLayer,
  Vector as VectorLayer,
  Image as ImageLayer,
} from "ol/layer";
import { GeoJSON, WKT } from "ol/format";
import { defaults, MousePosition } from "ol/control";
import { createStringXY } from "ol/coordinate";
import { LinearRing, LineString, Point, Polygon } from "ol/geom";
import { fromExtent } from "ol/geom/Polygon";
import {
  Style,
  Stroke,
  Text,
  Fill,
  Circle,
  Icon,
  IconImage,
  RegularShape,
} from "ol/style";
import Zoom from "ol/control/Zoom";

import mapNum from "@/assets/images/map-num.png";
import mapNpc from "@/assets/images/map-npc.png";
import mapLaw from "@/assets/images/map-law.png";

const { proxy } = getCurrentInstance();
const props = defineProps({
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "400px",
  },
  siteType: {
    type: String,
    default: "npc",
  },
  sourceData: {
    type: [Array, Object],
    default: [],
  },
  pSimRange: {
    type: Object,
    default: () => {
      return {
        min: 0,
        max: 1,
      };
    },
  },
});
const dialogVisible = ref(false);
const icingMap = ref(null);
const popup = ref(null);
const myPopup = ref(null);
const curObj = ref({});

const openScreen = () => {
  dialogVisible.value = true;
  icingMap.value = null;
  proxy.$nextTick(() => {
    initIcingMap("icingliveMap2");
    drawPoint();
  });
};

const closeScreen = () => {
  dialogVisible.value = false;
  icingMap.value = null;
  proxy.$nextTick(() => {
    initIcingMap("icingliveMap");
    drawPoint();
  });
};

//初始化地图
const initIcingMap = (id) => {
  //地图对象
  icingMap.value = new Map({
    target: id,
    controls: defaults({
      attribution: false,
      rotate: false,
      zoom: false,
    }),
    view: new View({
      projection: "EPSG:4326",
      center: [116.4074, 39.9042],
      zoom: 5,
      minZoom: 1,
      maxZoom: 18,
    }),
    layers: [],
    overlays: [],
    controls: [
      // new ZoomSlider(),
      new Zoom(),
    ],
  });
  //添加地图类型
  let baseMap = [
    // {
    //   name: "天地图矢量底图0",
    //   type: "cva_w",
    //   zindex: 0,
    //   url: "http://t4.tianditu.com/DataServer?T=vec_w&tk=31f0eb7d629d7b6fa27ce133ac024cf4&x={x}&y={y}&l={z}",
    // },

    {
      name: "天地图矢量底图0",
      type: "cva_w",
      zindex: 0,
      url: "https://tile1.tianditu.gov.cn/vts?t=vt&pk=jda&tk=75f0434f240669f4a2df6359275146d2&v=1.0",
    },

    // {
    //   name: "天地图矢量底图1",
    //   type: "cva_w",
    //   zindex: 1,
    //   url: "http://t4.tianditu.com/DataServer?T=cva_w&tk=31f0eb7d629d7b6fa27ce133ac024cf4&x={x}&y={y}&l={z}",
    // },
    // {
    //   name: "高德地图",
    //   type: "cia_w",
    //   zindex: 1,
    //   url: "http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&style=7&x={x}&y={y}&z={z}",
    // },

    // {
    //   name: "天地图影像注记",
    //   type: "cva_w",
    //   zindex: 2,
    //   url:
    //     "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    //     "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    //     "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    //     "&tk=31f0eb7d629d7b6fa27ce133ac024cf4",
    // },
    // {
    //   name: "天地图地形注记",
    //   type: "cta_w",
    //   zindex: 3,
    //   url:
    //     "http://t0.tianditu.gov.cn/cta_w/wmts?" +
    //     "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    //     "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    //     "&tk=31f0eb7d629d7b6fa27ce133ac024cf4",
    // },
  ];
  baseMap.map((item) => {
    createTianDiTuLayer(`tianditu_${item.type}`, item.url, item.zindex);
  });

  // createClusterLayer();
  addOverlay();
  singleclick();
};

//创建天地图类型
const createTianDiTuLayer = (layerId, url, zIndex) => {
  let tianDiTuLayer = new TileLayer({
    layerId: layerId,
    source: new XYZ({ url: url }),
    zIndex: zIndex,
  });
  icingMap.value.addLayer(tianDiTuLayer);
};

watch(
  () => props.sourceData,
  () => {
    drawPoint();
    let coordinates = [
      +props.sourceData[0].longitude,
      +props.sourceData[0].latitude,
    ];
    console.log("coordinates---", coordinates, props.sourceData);
    let view = icingMap.value.getView();
    // 获取当前缩放级别
    var currentZoom = view.getZoom();
    // 放大地图
    // view.setZoom(currentZoom + 1);
    view.animate({
      // 使用动画平滑放大到该点
      center: coordinates,
      zoom: currentZoom, // 放大级别
      duration: 500, // 动画持续时间，单位毫秒
    });
  },
  { deep: true }
);

const drawPoint = () => {
  if (icingMap.value) {
    let layers = icingMap.value.getLayers();
    let lastLayer = layers.item(layers.getLength() - 1);
    if (lastLayer.values_.zIndex == 500) {
      // 聚合图层
      icingMap.value.removeLayer(lastLayer);
    }
    createClusterLayer();
  }
};

const createClusterLayer = () => {
  // 添加随机点数据
  const clusterData = [];

  props.sourceData.map((val) => {
    clusterData.push([+val.longitude, +val.latitude]);
  });

  // 创建一个矢量数据源，并添加点数据
  // const vectorSource = new VectorSource({
  //   features: clusterData.map((coord) => new Feature(new Point(coord))),
  // });
  // console.log("clusterData---", clusterData);
  const vectorSource = new VectorSource({
    features: clusterData.map((coord, idx) => {
      const f = new Feature(new Point(coord));
      f.set("rawData", props.sourceData[idx]);
      return f;
    }),
  });

  // 创建一个聚合图层
  const clusterLayer = new VectorLayer({
    source: new Cluster({
      distance: 40, // 设置聚合的距离阈值
      source: vectorSource,
    }),
    style: function (feature) {
      // 根据聚合点的数量设置不同的样式
      const size = feature.get("features").length;

      const average =
        feature
          .get("features")
          .reduce((acc, curr) => acc + curr.values_.rawData.psim, 0) /
        feature.get("features").length;

      // console.log("size---", feature.get("features"), average);
      return new Style({
        image: new Circle({
          radius: Math.max(12, Math.min(28, 10 + size / 20)), // 半径根据聚合点数量进行调整
          stroke: new Stroke({ color: "white", width: 2 }),
          fill: new Fill({ color: getColor(average) }), // 通过psim获取颜色
        }),
        // image: new Icon({
        //   src: getIcon(size), // 聚合图标路径
        //   scale: 0.6, // 根据点的数量调整大小或样式等（需要额外逻辑）
        // }),
        text: new Text({
          text: formatNumber(average).toString(),
          fill: new Fill({ color: "#fff" }),
          // stroke: new Stroke({ color: '#000', width: 3 }),
          textAlign: "center",
          textBaseline: "middle",
          // offsetY: -2,
          // offsetX: -3,
        }),
      });
    },
    zIndex: 500,
  });

  function formatNumber(num) {
    if (Number.isInteger(num)) {
      return num; // 返回整数
    } else {
      return parseFloat(num.toFixed(2)); // 保留两位小数
    }
  }

  function getIcon(size) {
    if (size > 1) {
      return mapNum;
    } else if (size == 1 && props.siteType == "npc") {
      return mapNpc;
    } else {
      return mapLaw;
    }
  }

  const getColor = (value) => {
    const colorScale = echarts.color.lerp(
      (value - props.pSimRange?.min) /
        (props.pSimRange?.max - props.pSimRange?.min),
      ["#d94e5d", "#eac736", "#50a3ba"]
    );
    return colorScale;
  };

  // 将聚合图层添加到地图中
  icingMap.value.addLayer(clusterLayer);
};

const addOverlay = () => {
  // 创建Overlay
  popup.value = new Overlay({
    element: myPopup.value,
    positioning: "center-left",
    stopEvent: true,
    offset: [50, 40],
    autoPan: true,
    autoPanMargin: 100,
  });
  icingMap.value.addOverlay(popup.value);
};

const singleclick = () => {
  // 点击
  icingMap.value.on("singleclick", (e) => {
    // 判断是否点击在点上
    let feature = icingMap.value.forEachFeatureAtPixel(
      e.pixel,
      (feature) => feature
    );
    if (feature) {
      let coordinates = feature.getGeometry().getCoordinates();
      // 获取聚合点包含的所有 feature
      const features = feature.get("features");
      if (feature && features.length > 1) {
        // 你可以遍历 features，获取每个点的坐标或属性
        const points = features.map((f) => f.getGeometry().getCoordinates());
        console.log("被聚合的点位：", points, features);
        // 获取地图视图对象
        let view = icingMap.value.getView();
        // 获取当前缩放级别
        var currentZoom = view.getZoom();
        // 放大地图
        // view.setZoom(currentZoom + 1);
        view.animate({
          // 使用动画平滑放大到该点
          center: coordinates,
          zoom: currentZoom + 1, // 放大级别
          duration: 500, // 动画持续时间，单位毫秒
        });
      }
    }
  });
};

// const closePopup = () => {
//   shopPopup.value = false;
// };

onMounted(() => {
  initIcingMap("icingliveMap");
});
</script>

<style lang="scss">
.icingliveMap {
  position: relative;
  cursor: pointer;
}
.ol-zoom {
  background: #ffffff;
  box-shadow: 0px 8px 24px 0px rgba(149, 157, 165, 0.2);
  border-radius: 2px;
  right: 8px;
  bottom: 8px;
  top: auto;
  left: auto;
  cursor: pointer;
}
.ol-zoom button {
  // background-color: transparent; /* 透明背景 */
  // color: white; /* 文字颜色 */
  font-size: 23px; /* 字体大小 */
}
.ol-zoom button:hover {
  // background-color: #666;
  border: 0;
}

.fullscreen {
  position: absolute;
  top: 5px;
  right: 15px;
  width: 32px;
  height: 32px;
  text-align: center;
  // background: #ffffff;
  // box-shadow: 0px 8px 24px 0px rgba(149, 157, 165, 0.2);
  cursor: pointer;
  img {
    width: 20px;
    height: 20px;
    margin-top: 6px;
  }
}

.el-dialog__body {
  max-height: 90vh !important;
}

.popup {
  width: 350px;
  background: #fff;
  box-shadow: 0px 8px 24px 0px rgba(149, 157, 165, 0.2);
  padding: 24px;
  border-radius: 10px;
  .close {
    position: absolute;
    top: -14px;
    right: -14px;
    width: 28px;
    height: 28px;
    cursor: pointer;
  }
  .arrow {
    position: absolute;
    top: 50px;
    left: -38px;
    width: 0;
    height: 0;
    border-left: 28px solid transparent;
    border-right: 28px solid #fff;
    border-top: 22px solid transparent;
    border-bottom: 24px solid #fff;
    transform: rotate3d(1, 1, 1, -21deg);
  }
  .name-img {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    .n-i-l {
      flex: 1;
      margin-right: 16px;
      .name {
        font-weight: 500;
        font-size: 18px;
        color: #262626;
        margin-bottom: 16px;
      }
      .status {
        padding: 5px 14px;
        font-size: 14px;
        background: #ebebeb;
        border-radius: 2px;
        display: inline-block;
        &.pending {
          color: #00a870;
          background: #e3faf2;
        }
      }
    }
    .n-i-r {
      width: 138px;
      height: 96px;
      border-radius: 5px;
      position: relative;
      background: url("../../../assets/images/site_npc.png") no-repeat;
      background-size: 100% 100%;
      .tag {
        position: absolute;
        top: 0;
        left: 0;
        background: linear-gradient(to right, #f2931e, #f9611b);
        color: #fff;
        font-size: 14px;
        border-radius: 5px 0 5px 0;
        padding: 5px 10px;
      }
    }
  }
  .other {
    font-size: 16px;
    color: #666666;
  }
  .detail {
    text-align: center;
    font-size: 16px;
    color: #0078cd;
    cursor: pointer;
    margin-top: 16px;
    .el-icon {
      margin-left: 8px;
      transform: translateY(2px);
    }
  }
}
</style>

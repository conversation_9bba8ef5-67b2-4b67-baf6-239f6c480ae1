<template>
  <div
    class="advanced-map-container"
    :class="{ fullscreen: isFullscreen }"
    ref="containerRef"
  >
    <div ref="mapContainer" class="map-view"></div>
    <div class="fullscreen-btn" @click="toggleFullscreen">
      <img
        v-if="!isFullscreen"
        src="@/assets/images/fullscreen.png"
        alt="全屏"
      />
      <!-- <img v-else src="@/assets/images/exit-fullscreen.png" alt="退出全屏" /> -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";

const props = defineProps({
  lang: {
    type: String,
    default: "en",
  },
  center: {
    type: Array,
    default: () => [121.498586, 31.239637],
  },
  zoom: {
    type: Number,
    default: 5,
  },
  sourceData: {
    type: Array,
    default: () => [],
  },
});

const mapContainer = ref(null);
const mapInstance = ref(null);
let clusterInstance = null;
let markers = [];
const isFullscreen = ref(false);
const containerRef = ref(null);

// 动态加载高德地图 JS API 和 MarkerClusterer 插件
const loadAMap = () => {
  return new Promise((resolve, reject) => {
    // 已加载主库和插件
    if (window.AMap && window.AMap.MarkerClusterer) {
      console.log("AMap 和 MarkerClusterer 已加载");
      resolve(window.AMap);
      return;
    }

    console.log("开始加载 AMap...");
    // 主库和聚合插件
    const script = document.createElement("script");
    script.src =
      "https://webapi.amap.com/maps?v=1.4.15&key=9eb27ad62e3f59ee427b8dab3fdd24a4&plugin=AMap.MarkerClusterer";
    script.onload = () => {
      console.log("AMap 主库加载完成");
      // 检查 MarkerClusterer 是否可用
      if (window.AMap && window.AMap.MarkerClusterer) {
        console.log("MarkerClusterer 插件可用");
        resolve(window.AMap);
      } else {
        console.error("MarkerClusterer 插件未正确加载");
        reject(new Error("MarkerClusterer 插件未正确加载"));
      }
    };
    script.onerror = (error) => {
      console.error("AMap 加载失败:", error);
      reject(error);
    };
    document.head.appendChild(script);
  });
};

// 隐藏高德地图 logo 和版权信息的函数
const hideLogo = () => {
  // 使用 setTimeout 确保 DOM 元素已经渲染
  setTimeout(() => {
    // 隐藏 logo
    const logoElements = document.querySelectorAll(
      ".amap-logo, .amap-copyright"
    );
    logoElements.forEach((element) => {
      if (element) {
        element.style.display = "none";
        element.style.visibility = "hidden";
        element.style.opacity = "0";
      }
    });

    // 也可以通过地图容器查找
    if (mapContainer.value) {
      const containerLogos = mapContainer.value.querySelectorAll(
        ".amap-logo, .amap-copyright"
      );
      containerLogos.forEach((element) => {
        if (element) {
          element.style.display = "none";
          element.style.visibility = "hidden";
          element.style.opacity = "0";
        }
      });
    }

    console.log("已尝试隐藏地图 logo 和版权信息");
  }, 100);
};

const renderMap = async () => {
  try {
    console.log("开始渲染地图...");
    await loadAMap();

    if (mapInstance.value) {
      mapInstance.value.destroy();
      mapInstance.value = null;
    }

    mapInstance.value = new window.AMap.Map(mapContainer.value, {
      resizeEnable: true,
      center: props.center,
      zoom: props.zoom,
      lang: props.lang,
      // 隐藏高德地图 logo 和版权信息
      logoPosition: "RT", // 将 logo 移到右上角（然后通过 CSS 隐藏）
      copyright: {
        show: false, // 隐藏版权信息
      },
    });

    console.log("地图实例创建成功:", mapInstance.value);

    // 地图加载完成后隐藏 logo 和版权信息
    mapInstance.value.on("complete", () => {
      console.log("地图加载完成，开始隐藏 logo");
      hideLogo();
    });

    // 等待地图完全加载后再渲染聚合
    mapInstance.value.on("complete", () => {
      console.log("地图加载完成，开始渲染聚合");
      renderCluster();
    });
  } catch (error) {
    console.error("地图渲染失败:", error);
  }
};

const renderCluster = () => {
  if (!window.AMap || !mapInstance.value) {
    console.log("AMap 或 mapInstance 未准备好");
    return;
  }

  // 清理之前的聚合实例
  if (clusterInstance) {
    clusterInstance.setMap(null);
    clusterInstance = null;
  }
  markers = [];

  // 检查数据是否存在
  if (!props.sourceData || props.sourceData.length === 0) {
    console.log("没有数据需要渲染");
    return;
  }

  // 创建标记点
  props.sourceData.forEach((item, index) => {
    // 支持多种字段名格式
    const longitude = item.longitude || item.decimalLongitude || item.lng;
    const latitude = item.latitude || item.decimalLatitude || item.lat;
    const psim = item.psim || item.pSim || item.p_sim || 0;

    console.log(
      `处理数据项 ${index}:`,
      item,
      "经度:",
      longitude,
      "纬度:",
      latitude,
      "psim:",
      psim
    );

    if (
      longitude !== undefined &&
      latitude !== undefined &&
      longitude !== null &&
      latitude !== null &&
      longitude !== "" &&
      latitude !== "" &&
      !isNaN(Number(longitude)) &&
      !isNaN(Number(latitude))
    ) {
      // 创建 marker 配置
      const markerConfig = {
        position: [parseFloat(longitude), parseFloat(latitude)],
        extData: {
          psim: item.psim || item.pSim || item.p_sim || 0, // 支持多种 psim 字段名
          originalData: item,
        },
      };

      const marker = new window.AMap.Marker(markerConfig);

      // 为标记点添加点击事件，打开信息框
      marker.on("click", function () {
        // 构建信息框内容
        const infoContent = [];

        if (item.scientificName) {
          infoContent.push(`<div style="padding: 8px;">`);
          infoContent.push(
            `<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${item.scientificName}</h4>`
          );
          infoContent.push(`<div style="font-size: 12px; color: #666;">`);
          infoContent.push(
            `<p style="margin: 4px 0;"><strong>经度:</strong> ${longitude}</p>`
          );
          infoContent.push(
            `<p style="margin: 4px 0;"><strong>纬度:</strong> ${latitude}</p>`
          );
          if (item.psim || item.pSim || item.p_sim) {
            infoContent.push(
              `<p style="margin: 4px 0;"><strong>PSIM值:</strong> ${
                item.psim || item.pSim || item.p_sim
              }</p>`
            );
          }
          if (item.modified) {
            infoContent.push(
              `<p style="margin: 4px 0;"><strong>修改时间:</strong> ${item.modified}</p>`
            );
          }
          infoContent.push(`</div></div>`);
        } else {
          infoContent.push(`<div style="padding: 8px;">`);
          infoContent.push(
            `<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">位置信息</h4>`
          );
          infoContent.push(`<div style="font-size: 12px; color: #666;">`);
          infoContent.push(
            `<p style="margin: 4px 0;"><strong>经度:</strong> ${longitude}</p>`
          );
          infoContent.push(
            `<p style="margin: 4px 0;"><strong>纬度:</strong> ${latitude}</p>`
          );
          if (item.psim || item.pSim || item.p_sim) {
            infoContent.push(
              `<p style="margin: 4px 0;"><strong>PSIM值:</strong> ${
                item.psim || item.pSim || item.p_sim
              }</p>`
            );
          }
          infoContent.push(`</div></div>`);
        }

        // 创建信息框
        const infoWindow = new window.AMap.InfoWindow({
          content: infoContent.join(""),
          offset: new window.AMap.Pixel(0, -30),
        });

        // 打开信息框
        infoWindow.open(mapInstance.value, marker.getPosition());
      });

      markers.push(marker);

      // 如果是第一个标记点，默认打开信息框
      if (index === 0) {
        setTimeout(() => {
          marker.emit("click");
        }, 500);
      }
    } else {
      console.warn(`数据项 ${index} 的经纬度无效:`, { longitude, latitude });
    }
  });

  console.log("创建的 markers 数量:", markers.length);

  // 如果没有有效的标记点，直接返回
  if (markers.length === 0) {
    console.log("没有有效的标记点");
    return;
  }

  // 新增：点位有变化时自动将所有点的经纬度中心设置为地图中心
  if (markers.length > 0 && props.sourceData && props.sourceData.length > 0) {
    // 计算所有点的中心位置
    let totalLng = 0;
    let totalLat = 0;
    let validCount = 0;

    props.sourceData.forEach((item) => {
      const lng = item.longitude || item.decimalLongitude || item.lng;
      const lat = item.latitude || item.decimalLatitude || item.lat;

      if (
        lng !== undefined &&
        lat !== undefined &&
        lng !== null &&
        lat !== null &&
        !isNaN(Number(lng)) &&
        !isNaN(Number(lat))
      ) {
        totalLng += parseFloat(lng);
        totalLat += parseFloat(lat);
        validCount++;
      }
    });

    if (validCount > 0) {
      const centerLng = totalLng / validCount;
      const centerLat = totalLat / validCount;

      // 使用 setTimeout 确保地图已经完全初始化
      setTimeout(() => {
        if (mapInstance.value) {
          mapInstance.value.panTo([centerLng, centerLat]);
          console.log("地图中心已移动到:", [centerLng, centerLat]);
        }
      }, 100);
    }
  }
  const count = markers.length;

  // 自定义聚合标记渲染函数
  const _renderClusterMarker = function (context) {
    // 严格防御性判断，彻底避免 Vg 报错
    if (
      !context ||
      !context.marker ||
      typeof context.marker.setOffset !== "function" ||
      typeof context.marker.setContent !== "function"
    ) {
      return;
    }
    var factor = Math.pow(context.count / count, 1 / 18);
    var div = document.createElement("div");
    var Hue = 180 - factor * 180;
    var bgColor = "hsla(" + Hue + ",100%,50%,0.7)";
    var fontColor = "hsla(" + Hue + ",100%,20%,1)";
    var borderColor = "hsla(" + Hue + ",100%,40%,1)";
    var shadowColor = "hsla(" + Hue + ",100%,50%,1)";
    div.style.backgroundColor = bgColor;
    var size = Math.round(30 + Math.pow(context.count / count, 1 / 5) * 20);
    div.style.width = div.style.height = size + "px";
    div.style.border = "solid 1px " + borderColor;
    div.style.borderRadius = size / 2 + "px";
    div.style.boxShadow = "0 0 1px " + shadowColor;
    // 检测 sourceData 中是否有 scientificName 字段
    const hasScientificName =
      props.sourceData &&
      props.sourceData.length > 0 &&
      props.sourceData.some((item) => item.scientificName !== undefined);

    let displayValue;

    if (hasScientificName) {
      // 如果有 scientificName 字段，显示总数
      displayValue = context.count;
    } else {
      // 否则计算并显示 psim 平均值
      let psimSum = 0;
      let psimCount = 0;

      if (context.markers && context.markers.length > 0) {
        context.markers.forEach((marker) => {
          const markerData = marker.getExtData();
          if (
            markerData &&
            markerData.psim !== undefined &&
            markerData.psim !== null
          ) {
            psimSum += parseFloat(markerData.psim);
            psimCount++;
          }
        });
      }

      function formatNumber(num) {
        if (Number.isInteger(num)) {
          return num; // 返回整数
        } else {
          return parseFloat(num.toFixed(2)); // 保留两位小数
        }
      }

      displayValue = formatNumber(psimSum);
    }

    // 显示计算出的值（总数或平均值）
    div.innerHTML = displayValue;
    div.style.lineHeight = size + "px";
    div.style.color = fontColor;
    div.style.fontSize = "12px";
    div.style.textAlign = "center";
    div.style.fontWeight = "bold";
    context.marker.setOffset(new window.AMap.Pixel(-size / 2, -size / 2));
    context.marker.setContent(div);
  };

  try {
    // 创建聚合实例 - 注意参数顺序：map, markers, options
    clusterInstance = new window.AMap.MarkerClusterer(
      mapInstance.value,
      markers,
      {
        gridSize: 80,
        renderClusterMarker: _renderClusterMarker,
      }
    );
    console.log("聚合实例创建成功:", clusterInstance);
  } catch (error) {
    console.error("创建聚合实例失败:", error);
  }
};

const toggleFullscreen = () => {
  const el = containerRef.value;
  if (!isFullscreen.value) {
    if (el.requestFullscreen) el.requestFullscreen();
    else if (el.webkitRequestFullscreen) el.webkitRequestFullscreen();
    else if (el.mozRequestFullScreen) el.mozRequestFullScreen();
    else if (el.msRequestFullscreen) el.msRequestFullscreen();
  } else {
    if (document.exitFullscreen) document.exitFullscreen();
    else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
    else if (document.mozCancelFullScreen) document.mozCancelFullScreen();
    else if (document.msExitFullscreen) document.msExitFullscreen();
  }
};

const fullscreenChangeHandler = () => {
  const fsElement =
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement;
  isFullscreen.value = !!fsElement;
  setTimeout(() => {
    if (mapInstance.value) mapInstance.value.resize();
  }, 300);
};

onMounted(() => {
  renderMap();

  // 延迟执行隐藏 logo，确保地图完全加载
  setTimeout(() => {
    hideLogo();
  }, 1000);

  document.addEventListener("fullscreenchange", fullscreenChangeHandler);
  document.addEventListener("webkitfullscreenchange", fullscreenChangeHandler);
  document.addEventListener("mozfullscreenchange", fullscreenChangeHandler);
  document.addEventListener("MSFullscreenChange", fullscreenChangeHandler);
});

// 只在地图配置变化时重建地图
watch(
  () => [props.lang, props.center, props.zoom],
  () => {
    renderMap();
  }
);

// 只在 sourceData 变化时刷新聚合
watch(
  () => props.sourceData,
  () => {
    renderCluster();
  },
  { deep: true }
);
</script>

<style scoped>
.advanced-map-container {
  position: relative;
  width: 100%;
  height: 60vh;
  background: #f5f5f5;
}
.advanced-map-container.fullscreen {
  position: fixed !important;
  left: 0;
  top: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background: #fff;
}
.map-view {
  width: 100%;
  height: 100%;
}
.fullscreen-btn {
  position: absolute;
  top: -53px;
  right: -10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}
.fullscreen-btn img {
  width: 20px;
  height: 20px;
}

/* 隐藏高德地图 logo 和版权信息 */
:deep(.amap-logo) {
  display: none !important;
}

:deep(.amap-copyright) {
  display: none !important;
}

/* 备用方案：通过容器选择器 */
.map-container :deep(.amap-logo),
.map-container :deep(.amap-copyright) {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}
</style>

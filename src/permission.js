import router from "./router";
import { ElMessage } from "element-plus";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { isHttp } from "@/utils/validate";
import { isRelogin } from "@/utils/request";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/register"];

router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title);
    /* has token*/
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        useUserStore()
          .getInfo()
          .then(() => {
            isRelogin.show = false;
            usePermissionStore()
              .generateRoutes()
              .then((accessRoutes) => {
                // 根据roles权限生成可访问的路由表
                // accessRoutes.forEach((route) => {
                //   if (!isHttp(route.path)) {
                //     router.addRoute(route); // 动态添加可访问路由表
                //   }
                // });
                // next({ ...to, replace: true }); // hack方法 确保addRoutes已完成

                const hasRoute = router.hasRoute(to.name);
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                // 登陆完拦截一下 跳转有权限的第一个路由
                if (from.path == "/login") {
                  let path = getFullPaths(accessRoutes);
                  usePermissionStore().setAccessFirstRoute(path);
                  useAppStore().setSubMenu(`/${path.split("/")[1]}`);
                  next({ path, replace: true });
                } else {
                  let path = usePermissionStore().accessFirstRoute;
                  if (!hasRoute) {
                    // 如果该路由不存在，可能是动态注册的路由，它还没准备好，需要再重定向一次到该路由
                    if (to.path == "/") {
                      next({ path, replace: true });
                    } else {
                      next({ ...to, replace: true });
                    }
                  } else {
                    next();
                  }
                }
              });
          })
          .catch((err) => {
            useUserStore()
              .logOut()
              .then(() => {
                ElMessage.error(err);
                next({ path: "/" });
              });
          });
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

function getFullPaths(routes, parentPath = "") {
  let fullPaths = [];
  routes.forEach((route) => {
    if (!route.hidden) {
      const fullPath = parentPath + route.path;
      if (route.children && route.children.length > 0) {
        fullPaths = fullPaths.concat(
          getFullPaths(route.children, fullPath + "/")
        );
      } else if (route.query) {
        const queryObject =
          typeof route.query === "string"
            ? JSON.parse(route.query)
            : route.query;
        const queryString = new URLSearchParams(queryObject).toString();
        fullPaths.push(`${fullPath}?${queryString}`);
      } else {
        fullPaths.push(fullPath);
      }
    }
  });
  return fullPaths[0];
}

router.afterEach(() => {
  NProgress.done();
});

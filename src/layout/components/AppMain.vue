<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }" :key="routeKey">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component
            v-if="!route.meta.link"
            :is="Component"
            :key="route.path"
          />
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
import { useRoute } from "vue-router";
import iframeToggle from "./IframeToggle/index";
import useTagsViewStore from "@/store/modules/tagsView";
const route = useRoute();
const routeKey = computed(() => route.path + Math.random());

const tagsViewStore = useTagsViewStore();
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - $base-navbar-height);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: $base-navbar-height;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view + breadcrumb = 50 + 34 */
    min-height: calc(100vh - $base-navbar-height - 34px - 50px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

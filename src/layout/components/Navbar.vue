<template>
  <div class="navbar v-row-between v-flex">
    <div class="navbar-title v-flex v-pointer">
      <img class="logo" :src="logoUrl" alt="logo" />
      <div v-if="appStore.device !== 'mobile'">
        <div class="v-line-1">{{ title }}</div>
        <div class="eng-title v-line-1">{{ engTitle }}</div>
      </div>
    </div>
    <div class="v-flex">
      <div
        class="v-m-x-12 v-font-20 nav-menu v-line-1"
        :class="{ 'active-menu': isMenuPathMatched(menu) }"
        v-for="menu in topSubMenuList"
        :key="menu.path"
        @click.stop="changeSys(menu)"
      >
        {{ currentLanguage === "en" ? menu.name : menu.meta.title }}
      </div>
    </div>
    <div class="right-menu v-col-center v-line-1">
      <template v-if="appStore.device !== 'mobile'">
        <!-- <header-search id="header-search" class="right-menu-item" /> -->
        <!-- <error-log class="errLog-container right-menu-item hover-effect" /> -->
        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->
        <!-- <el-tooltip
          :content="
            settingsStore.sideTheme === 'theme-dark' ? '浅色模式' : '深色模式'
          "
          effect="dark"
          placement="bottom"
        >
          <theme-switch class="right-menu-item hover-effect" />
        </el-tooltip> -->
        <el-dropdown
          trigger="click"
          @command="handleLanguageChange"
          class="right-menu-item"
        >
          <span class="language-switch">
            {{
              currentLanguage === "zh"
                ? t("navbar.chinese")
                : t("navbar.english")
            }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh">{{
                t("navbar.chinese")
              }}</el-dropdown-item>
              <el-dropdown-item command="en">{{
                t("navbar.english")
              }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <div class="avatar-container">
        {{ t("navbar.welcome") }}：
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon style="position: relative; left: 5px; top: 0"
              ><caret-bottom
            /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>{{
                  t("navbar.personalCenter")
                }}</el-dropdown-item>
              </router-link>
              <el-dropdown-item
                command="setLayout"
                v-if="settingsStore.showSettings"
              >
                <span>{{ t("navbar.layoutSettings") }}</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>{{ t("navbar.logout") }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, getCurrentInstance } from "vue";
import { ElMessageBox } from "element-plus";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import useLanguageStore from "@/store/modules/language";
import { useRoute, useRouter } from "vue-router";
import { t } from "@/utils/i18n";
import { useLanguageChange } from "@/composables/useLanguageChange";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const languageStore = useLanguageStore();

const title = computed(
  () => settingsStore.bigTitle || import.meta.env.VITE_APP_TITLE
);
const engTitle = computed(
  () => settingsStore.smallTitle || import.meta.env.VITE_APP_ENG_TITLE
);

const currentLanguage = computed(() => languageStore.currentLanguage);

// 使用语言变化监听
useLanguageChange();

const permissionStore = usePermissionStore();
const topSubMenuList = computed(() =>
  permissionStore.topbarRouters.filter((route) => !route.hidden)
);

const logoUrl = ref(settingsStore.logoUrl);

watch(
  () => settingsStore.logoUrl,
  (newUrl) => {
    logoUrl.value = newUrl;
  },
  { immediate: true }
);

function toggleSideBar() {
  appStore.toggleSideBar();
}

const isMenuPathMatched = (menu) => {
  let parentPath = route?.matched[0]?.path;
  return parentPath == menu.path;
};
const changeSys = (menu) => {
  console.log(menu, "menu");
  appStore.setSubMenu(menu.path);
  proxy.$tab.closeAllPage().then(({ visitedViews }) => {
    console.log(visitedViews, "visitedViews");
  });

  let firstRoute = getAllPaths(
    permissionStore.defaultRoutes.filter(
      (route) => !route.hidden && route.path === menu.path
    )[0]
  );

  router.push(firstRoute);
};

function getAllPaths(item, prefix = "") {
  let fullPath = prefix ? `${prefix}/${item.path}` : item.path;
  if (item.children && item.children.length > 0) {
    fullPath = getAllPaths(item.children[0], fullPath);
  } else if (item.query) {
    const queryObject =
      typeof item.query === "string" ? JSON.parse(item.query) : item.query;
    const queryString = new URLSearchParams(queryObject).toString();
    fullPath += `?${queryString}`;
  }
  return fullPath;
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm(t("navbar.logoutConfirm"), t("navbar.tip"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then(() => {
        location.href = import.meta.env.VITE_APP_CONTEXT_PATH + "";
      });
    })
    .catch(() => {});
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}

const handleLanguageChange = (lang) => {
  languageStore.setLanguage(lang);
  // Remove the page reload
  // window.location.reload();
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.navbar {
  height: $base-navbar-height;
  width: 100%;
  overflow: hidden;
  position: fixed;
  background: #fff;
  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 10;
  background: #fff url("@/assets/images/newtitle.png") no-repeat center center /
    110% 210px;

  overflow: hidden;
  // box-shadow: 0 1px 4px #00152914;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;
      color: #fff;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        color: #fff;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .navbar-title {
    font-size: 25px;
    font-weight: bold;
    // background: url("@/assets/images/newtitle.png") no-repeat 0px center / 570%
    //   200%;
    color: #fff;

    .logo {
      width: 28px;
      height: 25px;
      margin: 20px 5px;
      background-size: 100% 100%;
    }

    .eng-title {
      font-size: 12px;
      color: #fff;
    }
  }

  .nav-menu {
    color: #e5eaf3;
    cursor: pointer;
    font-weight: bold;
    // font-size: 22px;
  }

  .active-menu {
    color: #40c5ff;
  }
}

.language-switch {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #fff;

  position: relative;
  top: 43px;
  padding-right: 5px;

  &:hover {
    color: #e5eaf3;
  }
}
</style>

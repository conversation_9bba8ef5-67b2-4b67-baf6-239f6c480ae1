{"name": "ruoyi-vue-plus", "version": "4.8.2", "description": "RuoYi-Vue-Plus后台管理系统", "author": "LionLi", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/RuoYi-Vue-Plus-UI.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@fortaine/fetch-event-source": "^3.0.6", "@vscode/markdown-it-katex": "^1.1.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "isomorphic-dompurify": "^2.24.0", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "katex": "^0.16.21", "marked": "^15.0.11", "nprogress": "0.2.0", "ol": "^10.4.0", "pinia": "2.0.22", "pinia-plugin-persistedstate": "^4.2.0", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "cesium": "^1.126.0", "sass": "1.56.1", "unocss": "^65.5.0", "unplugin-auto-import": "0.11.4", "unplugin-vue-setup-extend-plus": "0.4.9", "vite": "^3.2.3", "vite-plugin-cesium": "^1.2.23", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}
import { defineConfig, loadEnv } from "vite";
import path from "path";
import createVitePlugins from "./vite/plugins";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    build: {
      sourcemap: env.VITE_APP_ENV == "production" ? false : true,
      // sourcemap: false,
      minify: env.VITE_APP_ENV == "production" ? false : true,
      rollupOptions: {
        output: {
          // 配置 Cesium 的资源路径
          assetFileNames: (assetInfo) => {
            if (assetInfo.name === "Cesium.js") {
              return "cesium/[name]-[hash][extname]";
            }
            return "assets/[name]-[hash][extname]";
          },
        },
      },
    },
    // vite 相关配置
    server: {
      port: 6600,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        "/dev-api": {
          target: "http://jcy.nat300.top/baseService",
          // target: "http://************:8080",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, ""),
          // ⏱️ 拉长超时时间（默认60秒可能不够）
          // timeout: 600_000, // 600秒（10分钟）
          // proxyTimeout: 600_000,

          // // ⚙️ 开启流式转发，防止缓冲阻塞传输
          // configure: (proxy) => {
          //   proxy.on("proxyReq", (proxyReq) => {
          //     proxyReq.setHeader("Connection", "keep-alive");
          //     proxyReq.setTimeout(600_000); // socket 超时设置
          //   });
          //   proxy.on("proxyRes", (proxyRes) => {
          //     proxyRes.headers["transfer-encoding"] = "chunked";
          //   });
          // },
        },

        "/file": {
          target: "http://jcy.nat300.top/file",
          // target: "http://************:8080",
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/file/, ""),
          // ⏱️ 拉长超时时间（默认60秒可能不够）
          // timeout: 600_000, // 600秒（10分钟）
          // proxyTimeout: 600_000,

          // // ⚙️ 开启流式转发，防止缓冲阻塞传输
          // configure: (proxy) => {
          //   proxy.on("proxyReq", (proxyReq) => {
          //     proxyReq.setHeader("Connection", "keep-alive");
          //     proxyReq.setTimeout(600_000); // socket 超时设置
          //   });
          //   proxy.on("proxyRes", (proxyRes) => {
          //     proxyRes.headers["transfer-encoding"] = "chunked";
          //   });
          // },
        },
      },
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },
  };
});
